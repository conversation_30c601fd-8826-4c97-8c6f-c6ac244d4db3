# Pourquoi le fichier `list.json` n'est pas créé ?

## Résumé du problème

Lorsque vous appelez l'endpoint `/loader/list/sandbox`, le fichier `list.json` n'est pas créé sur GCS parce que l'application fonctionne en **mode mock**.

## Explication détaillée

### 1. Mode Mock activé
Quand `USE_MOCKS=true`, la fonction `create_file_with_content()` dans `gcs_utils.py` :
- Ne crée PAS de fichier réel sur Google Cloud Storage
- Affiche seulement un message de simulation :
  ```
  🔧 Mock GCS: Simulating file creation gs://{bucket}/{path} (content: X chars)
  ```

### 2. Flux normal (qui devrait créer list.json)

```
POST /loader/list/sandbox
  ↓
LoaderService.get_document_list()
  ↓
loader.get_document_list(source) → retourne documents[]
  ↓
TreatmentFileManager.write_document_list()
  ↓
gcs_utils.create_file_with_content() ← ICI le mock intercepte
```

Le fichier `list.json` devrait être créé dans le chemin :
```
gs://bucket/load_date/domain_code/source_code/list/list.json
```

### 3. Solutions

#### Option A : Désactiver les mocks (mode production)
```bash
# Démarrer sans USE_MOCKS
ENV=local pipenv run uvicorn src.kbotloadscheduler.main:app --host 0.0.0.0 --port 8092
```
✅ **Avantage** : Le fichier sera créé réellement sur GCS
❌ **Inconvénient** : Nécessite une configuration GCP valide

#### Option B : Modifier le mock (développement) - FAIT ✅
J'ai modifié `gcs_utils.py` pour créer des fichiers locaux en mode mock :

```python
def create_file_with_content(bucket_name, file_path, content):
    if is_mocking_enabled():
        print(f"🔧 Mock GCS: Simulating file creation...")

        # Créer des fichiers locaux pour le développement
        local_mock_dir = os.path.join(os.getcwd(), "tmp", "mock-gcs", bucket_name)
        os.makedirs(local_mock_dir, exist_ok=True)

        local_file_path = os.path.join(local_mock_dir, file_path.replace("/", "_"))
        with open(local_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"📁 Mock file created locally: {local_file_path}")
        return

    # Code normal pour GCS...
```

## Test de la solution

### Test simple
```bash
cd /Users/<USER>/IdeaProjects/kbot-load-scheduler
USE_MOCKS=true python -c "
import sys
sys.path.insert(0, 'src')
from kbotloadscheduler.gcs import gcs_utils
gcs_utils.create_file_with_content('test-bucket', 'test/list.json', '{\"test\": true}')
"
```

### Test complet avec serveur
```bash
cd /Users/<USER>/IdeaProjects/kbot-load-scheduler
python test_server_and_endpoint.py
```

## Résultat

Maintenant, avec la modification :

1. **En mode mock** (`USE_MOCKS=true`) :
   - Les fichiers sont créés localement dans `tmp/mock-gcs/`
   - Format : `tmp/mock-gcs/{bucket_name}/{path_with_underscores}`
   - Utile pour le développement et les tests

2. **En mode production** (`USE_MOCKS=false`) :
   - Les fichiers sont créés réellement sur GCS
   - Nécessite une configuration GCP valide

## Vérification

Après avoir appelé l'endpoint, vérifiez :

```bash
# Voir les fichiers créés en mode mock
ls -la tmp/mock-gcs/

# Trouver les fichiers list.json
find tmp/mock-gcs/ -name "*list*.json"

# Voir le contenu
cat tmp/mock-gcs/bucket-name/path_to_list.json
```
