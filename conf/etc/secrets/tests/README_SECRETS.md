# 🔐 Système de Secrets pour les Tests et le Développement Local

Ce document décrit le système de secrets utilisé pour les tests automatisés et le développement local.

## 🎯 Objectif

- **Tests automatisés (`env="tests"`)** : Isoler les tests de GCP en utilisant des fichiers de secrets locaux.
- **Développement local (`env="local"`)** : Permettre de lancer l'application localement contre de vraies APIs (Confluence, etc.) en utilisant un jeu de secrets dédiés.
- **Surcharge facile** : Permettre de surcharger n'importe quel secret (même en production) en plaçant un fichier local, ce qui est utile pour le débogage.

## 📁 Structure des répertoires de secrets

Il existe deux répertoires de secrets principaux, chacun avec un objectif différent :

```
conf/etc/secrets/
├── tests/         # 🔬 Pour les tests automatisés (env="tests")
│   ├── setup_secrets.sh
│   ├── confluence-credentials/
│   │   ├── secret.example
│   │   └── secret
│   └── ...
└── local/         # 💻 Pour le développement local (env="local")
    ├── confluence-credentials/
    │   └── secret
    └── ...
```

## 🔧 Comment ça fonctionne

Le `ConfigWithSecret` utilise une **architecture hybride** où les fichiers locaux sont **toujours prioritaires**.

### 🏗️ **Architecture de fallback**

La logique de récupération d'un secret est la suivante :

1.  **Fichier local (Priorité absolue)** : Le système cherche d'abord un fichier dans le chemin configuré.
2.  **Google Secret Manager (Fallback)** : Si le fichier local est absent ET que `env != "tests"`, le secret est récupéré depuis GCP.

### 🌍 **Comportement par environnement et répertoire**

| Environnement (`ENV`) | Répertoire utilisé par défaut | Client GCP | Objectif |
|-----------------------|-------------------------------|------------|----------|
| **`tests`** | `conf/etc/secrets/tests/` | ❌ Désactivé | Tests automatisés, CI/CD. **Ne contacte jamais GCP.** |
| **`local`** | `conf/etc/secrets/local/` | ✅ Activé | Développement local contre de vraies APIs. Les fichiers locaux sont utilisés en priorité. |
| Autre (`prod`, etc.) | *Aucun par défaut* | ✅ Activé | Production. Utilise GCP, mais peut être surchargé par un fichier local si le chemin est fourni. |

**Important :** Le chemin vers les secrets est configurable via la variable d'environnement `PATH_TO_SECRET_CONFIG`. C'est ainsi que l'environnement `local` pointe vers `conf/etc/secrets/local/`.

## 📝 Configuration des Secrets

### Format des secrets

**Le format exact de chaque secret est défini dans son fichier `secret.example`** situé dans `conf/etc/secrets/tests/`. Ces templates sont la **source unique de vérité** pour la structure attendue.

Pour tout secret, référez-vous toujours au fichier `.example` associé pour connaître le format JSON ou texte attendu.

## 🚀 Configuration pour les Tests Automatisés (`env="tests"`)

Utilisez le script `setup_secrets.sh` pour préparer l'environnement de tests. Ce script travaille **uniquement** dans le répertoire `conf/etc/secrets/tests/`.

### 🎯 **Méthode recommandée (automatique)**

```bash
# 1. Lancer le script de setup. Il copiera .example -> secret
#    dans le répertoire conf/etc/secrets/tests/
./conf/etc/secrets/tests/setup_secrets.sh

# 2. Éditer les fichiers 'secret' créés avec vos valeurs de test
nano conf/etc/secrets/tests/confluence-credentials/secret

# 3. Vérifier que les secrets sont bien remplis
./conf/etc/secrets/tests/setup_secrets.sh --verify
```

## 🚀 Configuration pour le Développement Local (`env="local"`)

Pour le développement local, vous devez créer manuellement votre propre jeu de secrets dans `conf/etc/secrets/local/`. Ce répertoire est ignoré par Git.

### 🔧 **Méthode manuelle**

1.  **Créez le répertoire `local` s'il n'existe pas :**
    ```bash
    mkdir -p conf/etc/secrets/local
    ```

2.  **Copiez les templates depuis `tests` et placez-les dans `local` :**
    ```bash
    # Exemple pour les credentials Confluence
    mkdir -p conf/etc/secrets/local/confluence-credentials
    cp conf/etc/secrets/tests/confluence-credentials/secret.example \
       conf/etc/secrets/local/confluence-credentials/secret
    ```

3.  **Éditez le fichier `secret` dans le répertoire `local` avec vos vrais credentials de développement :**
    ```bash
    nano conf/etc/secrets/local/confluence-credentials/secret
    ```

4.  **Lancez votre serveur en exportant les bonnes variables d'environnement :**
    ```bash
    export ENV=local
    export PATH_TO_SECRET_CONFIG=$(pwd)/conf/etc/secrets/local
    # ... autres variables
    pipenv run uvicorn ...
    ```

## 🔒 Sécurité et Bonnes Pratiques

### ⚠️ **RÈGLES CRITIQUES**

| ✅ **À FAIRE** | ❌ **À NE JAMAIS FAIRE** |
|----------------|--------------------------|
| Utiliser `secret.example` comme template | Committer des fichiers `secret` (dans `tests` ou `local`) |
| Utiliser des tokens de test avec permissions limitées | Partager des tokens par email/chat |
| Isoler les secrets `local` des secrets `tests` | Utiliser des tokens de production en développement |

### 🛡️ **Protection automatique**

Le fichier `.gitignore` est configuré pour ignorer tous les fichiers `secret`, ainsi que le répertoire `local` entier, protégeant ainsi contre les commits accidentels.

```bash
# Dans .gitignore
conf/etc/secrets/tests/*/secret
conf/etc/secrets/local/
```

## 📋 Checklist de configuration

- [ ] ✅ Vérifier que `.gitignore` protège bien les fichiers `secret` et le répertoire `local`.
- [ ] 🔬 Pour les **tests automatisés**, utiliser `setup_secrets.sh` pour générer les secrets dans `conf/etc/secrets/tests/`.
- [ ] 💻 Pour le **développement local**, créer et remplir manuellement les secrets dans `conf/etc/secrets/local/`.
- [ ] ✋ **NE JAMAIS** committer les fichiers `secret` ou le contenu du répertoire `local`.
- [ ] 🔒 Utiliser des tokens de test avec des permissions aussi limitées que possible.
