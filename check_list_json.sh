#!/bin/bash

echo "🔍 Vérification de la création de list.json"
echo "============================================"

# Chemin attendu basé sur votre requête curl
EXPECTED_PATH="gs://ofr-ekb-knowledgebot-work-dev-sandbox-dev/202507121316/Confluence31136/testconfluence27284/list/list.json"

echo "Fichier attendu: $EXPECTED_PATH"
echo

# Vérifier si le fichier existe
if gsutil ls "$EXPECTED_PATH" 2>/dev/null; then
    echo "✅ Fichier list.json trouvé!"

    # Afficher la taille
    SIZE=$(gsutil du "$EXPECTED_PATH" | awk '{print $1}')
    echo "📄 Taille: $SIZE bytes"

    # Afficher un extrait du contenu
    echo
    echo "📄 Contenu (premiers 500 chars):"
    gsutil cat "$EXPECTED_PATH" | head -c 500
    echo
    echo "..."

else
    echo "❌ Fichier list.json NON trouvé à l'emplacement attendu"
    echo
    echo "🔍 Recherche de fichiers list.json récents..."

    # Chercher dans toute la structure
    BUCKET="gs://ofr-ekb-knowledgebot-work-dev-sandbox-dev"
    gsutil ls -r "$BUCKET/**/*list*.json" 2>/dev/null | grep -E "(list\.json|list/)" | tail -10

    if [ $? -ne 0 ]; then
        echo "   Aucun fichier list.json trouvé dans le bucket"
    fi
fi

echo
echo "💡 Pour créer le fichier, assurez-vous que:"
echo "   1. Le serveur est démarré avec USE_MOCKS=false"
echo "   2. KBOT_WORK_BUCKET_PREFIX ne contient pas /test/"
echo "   3. L'endpoint est appelé avec la bonne URL"
