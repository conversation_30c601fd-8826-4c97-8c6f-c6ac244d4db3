#!/usr/bin/env python3
"""
Script de diagnostic pour mode USE_MOCKS=false
"""

import os
import sys
import json
import requests
from google.cloud import storage

def diagnose_gcs_access():
    """Diagnostique l'accès GCS"""
    print("🔍 Diagnostic GCS")
    print("=" * 40)

    try:
        # Test client GCS
        client = storage.Client()
        print(f"✅ Client GCS créé (projet: {client.project})")

        # Test accès au bucket
        bucket_name = "ofr-ekb-knowledgebot-work-dev-sandbox-dev"
        try:
            bucket = client.get_bucket(bucket_name)
            print(f"✅ Accès au bucket: {bucket_name}")

            # Test écriture
            test_blob = bucket.blob("test-diagnostic.txt")
            test_blob.upload_from_string("test diagnostic")
            print("✅ Écriture sur GCS fonctionne")

            # Nettoyage
            test_blob.delete()
            print("✅ Suppression sur GCS fonctionne")

        except Exception as e:
            print(f"❌ Erreur accès bucket: {e}")
            return False

    except Exception as e:
        print(f"❌ Erreur client GCS: {e}")
        return False

    return True

def check_getlist_file():
    """Vérifie le fichier getlist.json"""
    print("\n🔍 Vérification fichier getlist.json")
    print("=" * 40)

    file_path = "gs://ofr-ekb-knowledgebot-work-dev-sandbox-dev/202507121316/Confluence31136/testconfluence57819/getlist/getlist.json"

    try:
        client = storage.Client()
        bucket_name, blob_name = file_path.replace("gs://", "").split("/", 1)
        bucket = client.get_bucket(bucket_name)
        blob = bucket.get_blob(blob_name)

        if blob and blob.exists():
            print(f"✅ Fichier existe: {file_path}")
            content = blob.download_as_text()
            print(f"📄 Taille: {len(content)} chars")

            try:
                data = json.loads(content)
                print(f"✅ JSON valide")
                print(f"📊 Source type: {data.get('src_type', 'unknown')}")
                print(f"📊 Source code: {data.get('code', 'unknown')}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON invalide: {e}")
                return False
        else:
            print(f"❌ Fichier n'existe pas: {file_path}")
            return False

    except Exception as e:
        print(f"❌ Erreur vérification: {e}")
        return False

def test_endpoint_with_real_gcs():
    """Teste l'endpoint avec le vrai GCS"""
    print("\n🔍 Test endpoint avec GCS réel")
    print("=" * 40)

    # Vérifier que USE_MOCKS est bien false
    mocks_enabled = os.getenv("USE_MOCKS", "false").lower() == "true"
    print(f"Mode mocks: {mocks_enabled}")

    if mocks_enabled:
        print("⚠️ Attention: USE_MOCKS=true détecté!")
        print("   Pour tester avec le vrai GCS, utilisez USE_MOCKS=false")
        return False

    url = "http://127.0.0.1:8092/loader/list/sandbox"
    data = {
        "get_list_file": "gs://ofr-ekb-knowledgebot-work-dev-sandbox-dev/202507121316/Confluence31136/testconfluence57819/getlist/getlist.json"
    }

    try:
        print(f"📡 Appel API: {url}")
        response = requests.post(
            url,
            headers={
                'accept': 'application/json',
                'Content-Type': 'application/json'
            },
            data=json.dumps(data),
            timeout=30
        )

        print(f"📊 Status: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Succès! Documents: {len(result)}")

            # Vérifier si le fichier list.json a été créé
            expected_list_path = data["get_list_file"].replace("/getlist/getlist.json", "/list/list.json")
            print(f"\n📁 Vérification création list.json:")
            print(f"   Chemin attendu: {expected_list_path}")

            try:
                client = storage.Client()
                bucket_name, blob_name = expected_list_path.replace("gs://", "").split("/", 1)
                bucket = client.get_bucket(bucket_name)
                blob = bucket.get_blob(blob_name)

                if blob and blob.exists():
                    print("   ✅ Fichier list.json créé avec succès!")
                    content = blob.download_as_text()
                    print(f"   📄 Taille: {len(content)} chars")

                    # Montrer un extrait
                    try:
                        data = json.loads(content)
                        print(f"   📊 Documents dans list.json: {len(data.get('documents', []))}")
                    except:
                        pass

                    return True
                else:
                    print("   ❌ Fichier list.json NON créé")
                    print("   🔍 Vérification des fichiers dans le dossier...")

                    # Lister les fichiers dans le dossier
                    folder_path = "/".join(blob_name.split("/")[:-1]) + "/"
                    blobs = bucket.list_blobs(prefix=folder_path)
                    files = [b.name for b in blobs]
                    print(f"   📂 Fichiers trouvés ({len(files)}):")
                    for f in files[:10]:  # Limiter à 10
                        print(f"      - {f}")

                    return False

            except Exception as e:
                print(f"   ❌ Erreur vérification: {e}")
                return False

        else:
            print(f"❌ Erreur API: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Erreur test: {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🔬 DIAGNOSTIC MODE USE_MOCKS=false")
    print("=" * 50)

    # 1. Vérifier l'accès GCS
    gcs_ok = diagnose_gcs_access()

    # 2. Vérifier le fichier getlist
    getlist_ok = check_getlist_file() if gcs_ok else False

    # 3. Tester l'endpoint
    if gcs_ok and getlist_ok:
        endpoint_ok = test_endpoint_with_real_gcs()
    else:
        print("\n⚠️ Tests préliminaires échoués, pas de test endpoint")
        endpoint_ok = False

    # Résumé
    print("\n📋 RÉSUMÉ")
    print("=" * 20)
    print(f"GCS Access: {'✅' if gcs_ok else '❌'}")
    print(f"GetList File: {'✅' if getlist_ok else '❌'}")
    print(f"Endpoint Test: {'✅' if endpoint_ok else '❌'}")

    if not endpoint_ok:
        print("\n💡 SUGGESTIONS:")
        if not gcs_ok:
            print("   - Vérifier l'authentification GCS (gcloud auth login)")
            print("   - Vérifier les permissions sur le bucket")
        if not getlist_ok:
            print("   - Vérifier que le fichier getlist.json existe")
            print("   - Vérifier le format JSON du fichier")
        print("   - Vérifier les logs du serveur pour plus de détails")

if __name__ == "__main__":
    main()
