import json
from datetime import datetime
from testutils.mock_gcs import MockGcs
from kbotloadscheduler.bean.beans import DocumentBean, DocumentAnswerBean


class GcsRepoTestData:
    REPO_BUCKET = 'repo-bucket'
    PERIMETER_CODE = 'perimB'
    DOMAINE_CODE = 'domA'
    SOURCE_CODE = 'srcA1'
    LOAD_DATE = '202402121630'
    CONF_SOURCE = {
        'bucket': f'gs://{REPO_BUCKET}',
        'prefix': 'src1/domainA',
        'sa': '<EMAIL>'
    }
    SOURCE = {
        'id': 1, 'code': SOURCE_CODE, 'label': 'source A 1',  'src_type': 'gcs',
        'configuration': json.dumps(CONF_SOURCE), 'last_load_time': 1726156483, 'load_interval': 24,
        'domain_code': DOMAINE_CODE, 'force_embedding': False, 'perimeter_code': PERIMET<PERSON>_CODE
    }

    DATE_CREATED_A1 = datetime(2023, 9, 12, 12, 36, 24)
    DATE_CREATED_A2 = datetime(2024, 8, 11, 8, 12, 42)
    DATE_CREATED_B1 = datetime(2024, 9, 5, 19, 54, 6)
    DATE_CREATED_B2 = datetime(2024, 9, 14, 6, 42, 36)
    DATE_A1 = datetime(2024, 9, 12, 12, 36, 24)
    DATE_A2 = datetime(2024, 9, 11, 8, 12, 42)
    DATE_B1 = datetime(2024, 9, 10, 19, 54, 6)
    DATE_B2 = datetime(2024, 9, 14, 6, 42, 36)
    # As these dates are not visible in beans we must have a way to get them to check serailized values
    DATE_DICT = {
        'src1/domainA/diralpha/fileAlpha1.txt': {
            'creation_time': DATE_CREATED_A1, 'creation_str_time': '20230912123624',
            'modification_time': DATE_A1, 'modification_str_time': '20240912123624'
        },
        'src1/domainA/diralpha/fileAlpha2.txt': {
            'creation_time': DATE_CREATED_A2, 'creation_str_time': '20240811081242',
            'modification_time': DATE_A2, 'modification_str_time': '20240911081242'
        },
        'src1/domainA/dirbeta/fileBeta1.txt': {
            'creation_time': DATE_CREATED_B1, 'creation_str_time': '20240905195406',
            'modification_time': DATE_B1, 'modification_str_time': '20240910195406'
        },
        'src1/domainA/dirbeta/fileBeta2.txt': {
            'creation_time': DATE_CREATED_B2, 'creation_str_time': '20240914064203',
            'modification_time': DATE_B2, 'modification_str_time': '20240914064203'
        }
    }

    WORK_BUCKET = f'mon_bucket-{PERIMETER_CODE}'
    RELATIVE_OUTPUT = f'{LOAD_DATE}/{DOMAINE_CODE}/{SOURCE_CODE}'
    GET_LIST_FILE = f'{RELATIVE_OUTPUT}/getlist/getlist.json'
    GET_LIST_FILE_DONE = f'{RELATIVE_OUTPUT}/getlist/getlist.done'
    LIST_FILE = f'{RELATIVE_OUTPUT}/list/list.json'
    LIST_FILE_DONE = f'{RELATIVE_OUTPUT}/list/list.done'
    GET_DOC_FILE = f'{RELATIVE_OUTPUT}/getdoc/getdoc.json'
    GET_DOC_FILE_DONE = f'{RELATIVE_OUTPUT}/getdoc/getdoc.done'
    BASE_OUTPUT = f'gs://{WORK_BUCKET}/{RELATIVE_OUTPUT}'

    @classmethod
    def write_get_list(cls, mock_gcs: MockGcs):
        mock_gcs.add_bucket(cls.WORK_BUCKET)
        mock_gcs.add_blob(cls.WORK_BUCKET, cls.GET_LIST_FILE, True, json.dumps(cls.SOURCE))

    @classmethod
    def write_document_list(cls, mock_gcs: MockGcs, document_list: list[dict], file_path: str = LIST_FILE):
        mock_gcs.add_bucket(cls.WORK_BUCKET)
        mock_gcs.add_blob(cls.WORK_BUCKET, file_path, True,
                          json.dumps({'source': cls.SOURCE, 'documents': document_list}))

    @classmethod
    def write_document_get_file(cls, mock_gcs: MockGcs, document: dict):
        file_path = f'{cls.RELATIVE_OUTPUT}/getdoc/fileAlpha2.getdoc.json'
        mock_gcs.add_bucket(cls.WORK_BUCKET)
        mock_gcs.add_blob(cls.WORK_BUCKET, file_path, True,
                          json.dumps({'source': cls.SOURCE, 'document': document}))
        return file_path

    @classmethod
    def prepare_gcs_repo(cls, mock_gcs: MockGcs):
        mock_gcs.add_bucket(cls.REPO_BUCKET)
        # Files that should be listed
        mock_gcs.add_blob('repo-bucket', 'src1/domainA/diralpha/fileAlpha1.txt', True, 'file A1 alpha 1',
                          extra={'updated': cls.DATE_A1, 'time_created': cls.DATE_CREATED_A1})
        mock_gcs.add_blob('repo-bucket', 'src1/domainA/diralpha/fileAlpha2.txt', True, 'file A1 alpha 2',
                          extra={'updated': cls.DATE_A2, 'time_created': cls.DATE_CREATED_A2})
        mock_gcs.add_blob('repo-bucket', 'src1/domainA/dirbeta/fileBeta1.txt', True, 'file A1 beta 1',
                          extra={'updated': cls.DATE_B1, 'time_created': cls.DATE_CREATED_B1})
        mock_gcs.add_blob('repo-bucket', 'src1/domainA/dirbeta/fileBeta2.txt', True, 'file A1 beta 2',
                          extra={'updated': cls.DATE_B2, 'time_created': cls.DATE_CREATED_B2})
        # Files that should not listed
        mock_gcs.add_blob('repo-bucket', 'src2/domainA/diralpha/fileAlpha1.txt', True, 'file A2 alpha 1',
                          extra={'updated': cls.DATE_A1, 'time_created': cls.DATE_CREATED_A1})
        mock_gcs.add_blob('repo-bucket', 'src1/domainB/diralpha/fileAlpha2.txt', True, 'file B1 alpha 2',
                          extra={'updated': cls.DATE_A2, 'time_created': cls.DATE_CREATED_A2})
        mock_gcs.add_blob('repo-bucket', 'src2/domainA/dirbeta/fileBeta1.txt', True, 'file A2 beta 1',
                          extra={'updated': cls.DATE_B1, 'time_created': cls.DATE_CREATED_B1})
        mock_gcs.add_blob('repo-bucket', 'src1/domainB/dirbeta/fileBeta2.txt', True, 'file B1 beta 2',
                          extra={'updated': cls.DATE_B2, 'time_created': cls.DATE_CREATED_B2})

    @classmethod
    def get_document_list_for_source(cls):
        return [
            DocumentBean(id='domA|srcA1|src1/domainA/diralpha/fileAlpha1.txt',
                         path='gs://repo-bucket/src1/domainA/diralpha/fileAlpha1.txt',
                         name='src1/domainA/diralpha/fileAlpha1.txt',
                         modification_time=cls.DATE_A1),
            DocumentBean(id='domA|srcA1|src1/domainA/diralpha/fileAlpha2.txt',
                         path='gs://repo-bucket/src1/domainA/diralpha/fileAlpha2.txt',
                         name='src1/domainA/diralpha/fileAlpha2.txt',
                         modification_time=cls.DATE_A2),
            DocumentBean(id='domA|srcA1|src1/domainA/dirbeta/fileBeta1.txt',
                         path='gs://repo-bucket/src1/domainA/dirbeta/fileBeta1.txt',
                         name='src1/domainA/dirbeta/fileBeta1.txt',
                         modification_time=cls.DATE_B1),
            DocumentBean(id='domA|srcA1|src1/domainA/dirbeta/fileBeta2.txt',
                         path='gs://repo-bucket/src1/domainA/dirbeta/fileBeta2.txt',
                         name='src1/domainA/dirbeta/fileBeta2.txt',
                         modification_time=cls.DATE_B2)
        ]

    @classmethod
    def get_serialized_document_list_for_source(cls):
        return [
            {
                "id": "domA|srcA1|src1/domainA/diralpha/fileAlpha1.txt",
                "path": "gs://repo-bucket/src1/domainA/diralpha/fileAlpha1.txt",
                "name": "src1/domainA/diralpha/fileAlpha1.txt",
                "modification_time": "20240912123624",
            },
            {
                "id": "domA|srcA1|src1/domainA/diralpha/fileAlpha2.txt",
                "path": "gs://repo-bucket/src1/domainA/diralpha/fileAlpha2.txt",
                "name": "src1/domainA/diralpha/fileAlpha2.txt",
                "modification_time": "20240911081242",
            },
            {
                "id": "domA|srcA1|src1/domainA/dirbeta/fileBeta1.txt",
                "path": "gs://repo-bucket/src1/domainA/dirbeta/fileBeta1.txt",
                "name": "src1/domainA/dirbeta/fileBeta1.txt",
                "modification_time": "20240910195406",
            },
            {
                "id": "domA|srcA1|src1/domainA/dirbeta/fileBeta2.txt",
                "path": "gs://repo-bucket/src1/domainA/dirbeta/fileBeta2.txt",
                "name": "src1/domainA/dirbeta/fileBeta2.txt",
                "modification_time": "20240914064236",
            },
        ]

    @classmethod
    def get_answer_document_list_for_source(cls):
        return [DocumentAnswerBean(**document) for document in cls.get_serialized_document_list_for_source()]
