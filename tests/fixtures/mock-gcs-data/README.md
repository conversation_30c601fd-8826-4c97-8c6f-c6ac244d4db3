# Mock GCS Test Data

This directory contains example mock data for GCS testing. These files are committed to the repository as reference examples and documentation.

## Files

### Source Type Examples
- `getlist_confluence.json` - **Confluence** source type example
- `getlist_sharepoint.json` - **SharePoint** source type example
- `getlist_googledrive.json` - **Google Drive** source type example
- `document_list_example.json` - Example document list structure

## Source Type Configurations

Each source type has different configuration parameters:

### Confluence
```json
{
  "sourceType": "confluence",
  "confluence_url": "https://espace.agir.orange.com/",
  "space_key": "VODCASTV",
  "labels": "ravenne",
  "child_page_depth": "15",
  "include_attachements": "true"
}
```

### SharePoint
```json
{
  "sourceType": "sharepoint",
  "sharepoint_url": "https://company.sharepoint.com/sites/knowledge",
  "site_id": "abc123-def456-ghi789",
  "drive_id": "b!xyz789",
  "folder_path": "/Shared Documents/Knowledge Base",
  "include_subfolders": "true",
  "file_types": "docx,xlsx,pptx,pdf"
}
```

### Google Drive
```json
{
  "sourceType": "googledrive",
  "folder_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
  "include_subfolders": "true",
  "shared_drive_id": "",
  "file_types": "docs,sheets,slides,pdf"
}
```

## Mock Bucket Mapping

When `USE_MOCK=true`, the following buckets are created:
- `gs://mock-bucket-ebotman/test-data/getlist.json` (Confluence)
- `gs://mock-bucket-mktsearch/test-data/getlist.json` (SharePoint)
- `gs://mock-bucket-devtools/test-data/getlist.json` (Google Drive)

## Usage

1. Start the server with GCS mocking: `make start-mock`
2. Test different source types:
   ```bash
   # Test Confluence
   curl -X POST http://localhost:8092/loader/list/ebotman \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-ebotman/test-data/getlist.json"}'

   # Test SharePoint
   curl -X POST http://localhost:8092/loader/list/mktsearch \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-mktsearch/test-data/getlist.json"}'

   # Test Google Drive
   curl -X POST http://localhost:8092/loader/list/devtools \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-devtools/test-data/getlist.json"}'
   ```

## Note

These are **committed example files** for documentation. Temporary test data should go in `debug-tests/mock-gcs-data/` (gitignored).

## How to Update or Create Source Types

### Updating an Existing Source Type

If the `getlist.json` format has changed for an existing source type (e.g., Confluence):

1. **Update the example file**: Edit the relevant `getlist_*.json` file in this directory
2. **Update the mock configuration**: Modify `src/kbotloadscheduler/gcs/mock_gcs_config.py`:
   ```python
   # Update the source_type_configs dictionary
   source_type_configs = {
       "confluence": {
           "sourceType": "confluence",
           "confluence_url": "https://espace.agir.orange.com/",
           "space_key": "VODCASTV",
           "labels": "ravenne",
           "child_page_depth": "15",
           "include_attachements": "true",
           # Add new fields here
           "new_field": "new_value"
       },
       # ...existing code...
   }
   ```
3. **Update this documentation**: Add the new configuration parameters to the relevant section above
4. **Test the changes**: Run `make start-mock` and `make test-mock-endpoint` to verify

### Creating a New Source Type

To add support for a new source type (e.g., "onedrive"):

1. **Create an example file**: Add `getlist_onedrive.json` with the complete getlist structure:
   ```json
   {
     "id": 1079611287,
     "code": "testonedrive99999",
     "label": "Test OneDrive Source",
     "src_type": "onedrive",
     "configuration": "{\"sourceCurrentLabel\":\"Test OneDrive Source\",\"sourceType\":\"onedrive\",\"onedrive_url\":\"https://company-my.sharepoint.com/\",\"folder_path\":\"/Documents/Knowledge\",\"include_subfolders\":\"true\"}",
     "last_load_time": 1750069757,
     "next_load_time": 1750156157,
     "load_interval": 24,
     "force_embedding": false,
     "domain_id": 1,
     "domain_code": "TestDomain"
   }
   ```

2. **Add to mock configuration**: Update `src/kbotloadscheduler/gcs/mock_gcs_config.py`:
   ```python
   # Add to source_type_configs
   source_type_configs = {
       # ...existing configs...
       "onedrive": {
           "sourceType": "onedrive",
           "onedrive_url": "https://company-my.sharepoint.com/",
           "folder_path": "/Documents/Knowledge",
           "include_subfolders": "true",
           "file_types": "docx,xlsx,pptx,pdf"
       }
   }

   # Add to perimeter_configs
   perimeter_configs = {
       # ...existing configs...
       "research": {
           "code": "testonedrive99999",
           "label": "Research OneDrive",
           "domain_code": "Research",
           "src_type": "onedrive",
           "id": 1079611287
       }
   }
   ```

3. **Update documentation**: Add the new source type to this README:
   ```markdown
   ### OneDrive
   ```json
   {
     "sourceType": "onedrive",
     "onedrive_url": "https://company-my.sharepoint.com/",
     "folder_path": "/Documents/Knowledge",
     "include_subfolders": "true",
     "file_types": "docx,xlsx,pptx,pdf"
   }
   ```

4. **Update Makefile**: Add a test command for the new source type in the `test-mock-endpoint` target
5. **Test the new source type**:
   ```bash
   make start-mock
   curl -X POST http://localhost:8092/loader/list/research \
     -H "Content-Type: application/json" \
     -d '{"get_list_file": "gs://mock-bucket-research/test-data/getlist.json"}'
   ```

### Configuration Structure Notes

Each source type's configuration is stored as a JSON string in the `configuration` field of the getlist structure. The configuration must include:

- `sourceCurrentLabel`: Human-readable label for the source
- `sourceType`: The technical source type identifier
- **Source-specific fields**: Parameters unique to that source type (URLs, credentials, filters, etc.)

### Best Practices

1. **Consistent naming**: Use `getlist_{sourcetype}.json` for example files
2. **Realistic data**: Use realistic URLs and configuration values in examples
3. **Unique IDs**: Ensure each source type example has a unique `id` and `code`
4. **Test thoroughly**: Always test new configurations with both mock and integration tests
5. **Document parameters**: Explain what each configuration parameter does
