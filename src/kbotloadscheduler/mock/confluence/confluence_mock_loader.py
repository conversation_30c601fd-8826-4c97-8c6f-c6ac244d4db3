"""
Confluence Loader for kbot-loader-scheduler
Mock mode
"""
import logging
from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.secret.secret_manager import ConfigWithSecret
from kbotloadscheduler.loader.abstract_loader import AbstractLoader
from typing import Any


logger = logging.getLogger(__name__)


class ConfluenceMockLoader(AbstractLoader):
    """Mock Loader for Confluence"""

    ID_SEPARATOR = "|"

    def __init__(self, config: ConfigWithSecret):
        """Initialize the Confluence loader.

        Args:
            config: Configuration with secrets
        """
        super().__init__("confluence")

    # --- API publique requise par AbstractLoader ---

    def get_document_list(self, source: SourceBean) -> list[DocumentBean]:
        """Get list of documents from Confluence source with advanced filtering."""
        print("🔧 ConfluenceLoader operating in mock mode - returning mock document list")
        return self._generate_mock_documents(source)

    def get_document(self, source: SourceBean, document: DocumentBean, output_path: str) -> dict[str, Any]:
        """Download a document from Confluence to GCS with production optimizations.
        """
        print("🔧 ConfluenceLoader.get_document() called in mock mode - returning empty metadata")
        return {}

    def _generate_mock_documents(self, source: SourceBean) -> list[DocumentBean]:
        """Generate mock documents including some attachments for testing."""
        print("🔧 Generating mock Confluence documents with attachments")

        from datetime import datetime
        import json

        # Parse configuration pour comprendre ce qui est demandé
        try:
            config_dict = json.loads(source.configuration)
            include_attachments = config_dict.get("include_attachments", False)
            print(f"   📎 include_attachments = {include_attachments}")
        except (json.JSONDecodeError, KeyError, TypeError):
            include_attachments = False

        documents = []

        # 1. Pages mockées
        base_id_prefix = f"{source.domain_code}|{source.code}"

        # Page 1 - Simple
        page1 = DocumentBean(
            id=f"{base_id_prefix}|123456",
            name="Guide de démarrage VODCASTV",
            path="/spaces/VODCASTV/pages/123456/Guide+de+démarrage+VODCASTV",
            modification_time=datetime.now()
        )
        documents.append(page1)

        # Page 2 - Avec attachements (si activés)
        page2 = DocumentBean(
            id=f"{base_id_prefix}|789012",
            name="Documentation technique avec annexes",
            path="/spaces/VODCASTV/pages/789012/Documentation+technique+avec+annexes",
            modification_time=datetime.now()
        )
        documents.append(page2)

        # 2. Attachements mockés (si activés)
        if include_attachments:
            print("   📎 Adding mock attachments...")

            # Get file extension filtering from config
            # Default to common document types if not specified
            default_extensions = ["pdf", "docx", "pptx", "xlsx", "png", "jpg", "jpeg"]
            file_extensions = config_dict.get("file_extensions", default_extensions)
            print(f"   📋 Configured file extensions: {file_extensions}")

            # Mock attachments with different extensions
            mock_attachments = [
                ("rapport_technique.pdf", "pdf"),
                ("procedure_installation.docx", "docx"),
                ("architecture_diagram.png", "png"),
                ("presentation_slides.pptx", "pptx"),
                ("data_analysis.xlsx", "xlsx"),
                ("quarterly_report.pptx", "pptx")
            ]

            attachment_count = 0
            for filename, extension in mock_attachments:
                # Apply file extension filtering like the real loader does
                if extension.lower() in [ext.lower().lstrip('.') for ext in file_extensions]:
                    att = DocumentBean(
                        id=f"{base_id_prefix}|att_{extension}_{attachment_count:03d}",
                        name=filename,
                        path=f"/wiki/download/attachments/789012/{filename}",
                        modification_time=datetime.now()
                    )
                    documents.append(att)
                    attachment_count += 1
                    print(f"   ✅ Added attachment: {filename} (extension: {extension})")
                else:
                    print(f"   ⏭️  Skipped attachment: {filename} (extension: {extension} not in {file_extensions})")

            print(f"   📊 Added {attachment_count} filtered mock attachments")
        else:
            print("   ⏭️  Attachments disabled, skipping")

        print(f"   📊 Total mock documents: {len(documents)}")

        return documents
