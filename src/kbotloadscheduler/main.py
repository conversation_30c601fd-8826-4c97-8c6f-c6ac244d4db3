from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
logging.basicConfig(level=logging.INFO)

from .dependency.container import Container
from .mock import is_mocking_enabled
from .route import schedule_routes
from .route import sources_routes
from .route import loader_routes
from .route import document_routes

DESCRIPTION = """
Le load-scheduler permet d'automatiser l'embedding des documents
  - récupération des sources à charger
  - récupération des listes de documents présents dans chaque source
  - comparaison de la liste des documents de la source avec la liste des documents déjà dans la base vecteur
  - récupération des nouveaux documents à embedder de la source vers GCP
  - appel de l'embedding pour ajouter les documents dans la base vecteur
  - suppression de la base vecteur des documents qui ne sont plus dans la source
"""

ROUTES = [
    schedule_routes,
    sources_routes,
    loader_routes,
    document_routes,
]

logger = logging.getLogger('uvicorn.error')
logger.setLevel(logging.DEBUG)


def create_app() -> FastAPI:
    """Création de l'application

    La création de l'application lance l'écoute sur le port $PORT (8000 par défaut).
    On crée :
      - l'instance de Container qui permet d'injecter les dépendances entre les différents modules
      - la configuration "middleware" pour autoriser des connexion cross origin (si appel depuis ihm)
      - les différentes routes à servir
    """
    container = get_dependency_container()
    info_environnement = f"environnement : {container.config.get('env')}"
    info_version = f"version : {container.config.get('version')}"

    my_app = FastAPI(
        title="API load scheduler pour l'Enabler Knowledge Bot",
        description=DESCRIPTION + "\n\n" + info_environnement + "\n\n" + info_version,
        openapi_tags=build_tags_metadata(ROUTES)
    )

    origins = [
        "*"
    ]
    my_app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"]
    )

    my_app.container = container
    include_routes(my_app, ROUTES)
    return my_app

def get_dependency_container():
    if is_mocking_enabled():
        from .mock.mock_container import MockContainer
        return MockContainer()
    return Container()

def build_tags_metadata(routes_modules):
    tags_metadata = []
    for routes_module in routes_modules:
        tags_metadata.append({
            'name': routes_module.TAG,
            'description': routes_module.DESCRIPTION,
            'summary': routes_module.DESCRIPTION
        })
    return tags_metadata


def include_routes(my_app, routes_modules):
    for routes_module in routes_modules:
        my_app.include_router(routes_module.router, tags=[routes_module.TAG])


app = create_app()
