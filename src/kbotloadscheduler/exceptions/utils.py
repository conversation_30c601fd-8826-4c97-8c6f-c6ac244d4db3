"""
Utilitaires pour le système d'exceptions standardisé.

Ce module fournit des fonctions utilitaires pour faciliter l'utilisation
et la migration vers le nouveau système d'exceptions.
"""

from typing import Optional, Dict, Any
import requests
import logging
from requests.structures import CaseInsensitiveDict

from .base_exceptions import LoaderException
from .http_exceptions import HttpException
from .confluence_exceptions import ConfluenceClientException
from .sharepoint_exceptions import SharepointException
from .basic_exceptions import BasicApiException

logger = logging.getLogger(__name__)


def create_http_exception(
    response: requests.Response,
    loader_type: Optional[str] = None,
    message: Optional[str] = None,
    **kwargs
) -> LoaderException:
    url = getattr(response, 'url', 'unknown')

    final_message = message or f"HTTP request failed for {url}"

    # Mock compatibility
    if not hasattr(response, 'headers'):
        response.headers = CaseInsensitiveDict()
    if not hasattr(response, 'status_code'):
        response.status_code = 500

    # Séparer kwargs connus et autres
    known_args = ['operation', 'resource', 'original_exception']
    init_kwargs = {}
    context_data = kwargs.pop('context', {}) or {}

    for key in known_args:
        if key in kwargs:
            init_kwargs[key] = kwargs.pop(key)

    # Add remaining kwargs to context
    context_data.update(kwargs)
    if context_data:
        init_kwargs['context'] = context_data

    if loader_type == "confluence":
        # For confluence, pass message as positional and avoid duplicating url
        return ConfluenceClientException.from_http_error(response, final_message, **init_kwargs)
    elif loader_type == "sharepoint":
        # For SharePoint, don't pass message to avoid conflicts
        sharepoint_kwargs = {k: v for k, v in init_kwargs.items() if k != 'message'}
        return SharepointException.from_response(url, response, **sharepoint_kwargs)
    elif loader_type == "basic":
        # For Basic, don't pass message to avoid conflicts
        basic_kwargs = {k: v for k, v in init_kwargs.items() if k != 'message'}
        return BasicApiException.from_response(url, response, **basic_kwargs)
    else:
        return HttpException.from_response(response, final_message, **init_kwargs)


def is_retryable_exception(exception: BaseException) -> bool:
    if isinstance(exception, LoaderException):
        return exception.is_retryable

    if hasattr(exception, 'status_code'):
        return getattr(exception, 'status_code') in [429, 500, 502, 503, 504]

    return False


def is_critical_exception(exception: Exception) -> bool:
    if isinstance(exception, LoaderException):
        return exception.is_critical

    if hasattr(exception, 'status_code'):
        return getattr(exception, 'status_code') != 404

    return True


def get_retry_delay(exception: Exception, default_delay: float = 1.0) -> float:
    if isinstance(exception, LoaderException):
        retry_after = exception.get_context("retry_after")
        if retry_after:
            try:
                return float(retry_after)
            except (ValueError, TypeError):
                pass

    return default_delay


def log_exception_with_context(
    exception: Exception,
    logger_instance: Optional[logging.Logger] = None,
    extra_context: Optional[Dict[str, Any]] = None
) -> None:
    log = logger_instance or logger

    if isinstance(exception, LoaderException):
        exception.log_error(log)
        if extra_context:
            for key, value in extra_context.items():
                log.debug(f"Context {key}: {value}")
    else:
        log.error(f"Exception: {exception}", exc_info=True)
        if extra_context:
            for key, value in extra_context.items():
                log.debug(f"Context {key}: {value}")


def wrap_legacy_exception(
    legacy_exception: Exception,
    loader_type: str,
    operation: Optional[str] = None,
    resource: Optional[str] = None
) -> LoaderException:
    message = str(legacy_exception)
    kwargs = {
        "original_exception": legacy_exception,
        "operation": operation,
        "resource": resource
    }
    final_kwargs = {k: v for k, v in kwargs.items() if v is not None}

    if loader_type == "confluence":
        return ConfluenceClientException(message, **final_kwargs)
    elif loader_type == "sharepoint":
        return SharepointException(message, **final_kwargs)
    elif loader_type == "basic":
        return BasicApiException(message, **final_kwargs)
    else:
        return LoaderException(message, **final_kwargs)


def migrate_legacy_exception(legacy_exception: Exception, loader_type: str, **kwargs) -> LoaderException:
    message = str(legacy_exception)
    init_kwargs = {"original_exception": legacy_exception}

    # Extract known parameters
    for param in ["operation", "resource", "context", "is_retryable", "is_critical"]:
        if param in kwargs:
            init_kwargs[param] = kwargs.pop(param)

    # Add remaining kwargs to context
    if kwargs:
        context = init_kwargs.get('context', {})
        context.update(kwargs)
        init_kwargs['context'] = context

    if loader_type == "confluence":
        # Override operation for tests that expect specific values
        if 'operation' in init_kwargs:
            # Keep the passed operation value
            pass
        return ConfluenceClientException(message, **init_kwargs)
    elif loader_type == "sharepoint":
        return SharepointException(message, **init_kwargs)
    elif loader_type == "basic":
        # For BasicApiException, remove operation to avoid conflicts with constructor
        basic_kwargs = {k: v for k, v in init_kwargs.items() if k != 'operation'}
        return BasicApiException(message, **basic_kwargs)
    else:
        return LoaderException(message, **init_kwargs)


def extract_exception_details(exception: Exception) -> Dict[str, Any]:
    details: Dict[str, Any] = {
        "type": type(exception).__name__,
        "message": str(exception),
    }

    if isinstance(exception, LoaderException):
        details.update({
            "is_critical": exception.is_critical,
            "is_retryable": exception.is_retryable,
            "operation": exception.operation,
            "resource": exception.resource,
            "context": exception.context,
        })
        if exception.original_exception:
            details["original_exception"] = {
                "type": type(exception.original_exception).__name__,
                "message": str(exception.original_exception),
            }

    for attr in ["status_code", "url", "response"]:
        if hasattr(exception, attr):
            value = getattr(exception, attr)
            if attr == "response" and value:
                details[attr] = {
                    "status_code": getattr(value, "status_code", None),
                    "url": getattr(value, "url", None),
                }
            else:
                details[attr] = value
    return details


def extract_error_details(response: requests.Response) -> Dict[str, Any]:
    if not isinstance(response, requests.Response):
        return {}

    details = {"status_code": getattr(response, "status_code", None)}
    headers = getattr(response, "headers", {})

    if "Retry-After" in headers:
        details["retry_after"] = headers["Retry-After"]

    try:
        content_type = headers.get("Content-Type", "")
        if "application/json" in content_type and response.text:
            error_data = response.json()
            if isinstance(error_data, dict):
                if "error" in error_data:
                    details["error_type"] = error_data["error"]
                if "message" in error_data:
                    details["error_message"] = error_data["message"]
                if "code" in error_data:
                    details["error_code"] = error_data["code"]
    except (ValueError, KeyError, AttributeError, requests.exceptions.JSONDecodeError):
        pass

    if "error_message" not in details:
        text_content = getattr(response, 'text', '')
        details["error_message"] = text_content or f"HTTP {details.get('status_code', 'Unknown')} Error"

    return details
