from typing import List
from datetime import datetime
import time

from .document_service import DocumentService
from .loader_service import LoaderService
from .sources_service import SourcesService
from ..apicall.kbot_back_api import KbotBackApi
from ..bean.beans import TreatmentBean, LOAD_DATE_FORMAT
from ..gcs.treatment_file_manager import TreatmentFileManager
from ..logging.load_scheduler_logging import log_exception, log_action, log_treatments_launched, log_message


class ScheduleService:
    """Service permettant de lister les traitements à réaliser sur les deux derniers jours

    Ce service va appeler l'api kbot-back pour récupérer les perimetres
    et liste tous les fichiers à traiter
    """

    MAX_EXECUTION_TIME = 200  # launch treatments is aborted after 200s running
    TOKEN_LIFE = MAX_EXECUTION_TIME * 3  # token is ignored if older than 3 x MAX_EXECUTION_TIME

    URL_BY_TYPE = {
        TreatmentFileManager.GET_LIST: '/loader/list/{perimeter_code}',
        TreatmentFileManager.LIST: '/document/compare/{perimeter_code}',
        TreatmentFileManager.GET_DOC: '/loader/document/{perimeter_code}',
        TreatmentFileManager.DOCS: '/document/embedd/{perimeter_code}',
        TreatmentFileManager.REMOVE_DOC: '/document/remove/{perimeter_code}'
    }
    PARAM_KEY_BY_TYPE = {
        TreatmentFileManager.GET_LIST: 'get_list_file',
        TreatmentFileManager.LIST: 'repo_document_list_file',
        TreatmentFileManager.GET_DOC: 'get_document_file',
        TreatmentFileManager.DOCS: 'embedd_document_metadata_file',
        TreatmentFileManager.REMOVE_DOC: 'remove_document_file'
    }

    tasks_to_execute = {}

    def __init__(self,
                 kbot_back_api: KbotBackApi,
                 treatment_file_manager: TreatmentFileManager,
                 sources_service: SourcesService,
                 document_service: DocumentService,
                 loader_service: LoaderService

                 ) -> None:
        """Constructeur

        :param kbot_back_api: classe d'appel de l'api kbot_back
        :param treatment_file_manager: classe permettant d'écrire le fichier get_list
        """
        self._kbot_back_api: KbotBackApi = kbot_back_api
        self._treatment_file_manager: TreatmentFileManager = treatment_file_manager
        self._sources_service: SourcesService = sources_service
        self._document_service: DocumentService = document_service
        self._loader_service: LoaderService = loader_service

    def list_treatments(self, date: str) -> List[TreatmentBean]:
        """Liste des traitements à réaliser

        :param date: date de récupération : on va lister les load date sur les dernières 24 heures
        :return: liste des traitements à récupérer
        """

        treatment_date = datetime.strptime(date, LOAD_DATE_FORMAT)
        perimeters = self._kbot_back_api.get_perimeters()
        treatments = []
        for perimeter in perimeters:
            perimeter_treatments = self.list_treatments_for_perimeter(perimeter.get("code"), treatment_date)
            treatments.extend(perimeter_treatments)
        return treatments

    def list_treatments_for_perimeter(self, perimeter_code: str, treatment_date: datetime) -> List[TreatmentBean]:
        """Liste les traitements pour un périmetre

        :param perimeter_code: le périmètre pour lequel lister les sources à récupérer
        :param treatment_date: date référence pour récupérer les traitements par rapport à cette date
        :return: liste des traitements à récupérer
        """
        treatments = []
        load_date_list = self._treatment_file_manager.get_load_dates(perimeter_code, treatment_date)
        for load_date in load_date_list:
            treatments_files = self._treatment_file_manager.get_treatments_files(perimeter_code, load_date)
            for treatment_file, treatment_status in treatments_files.items():
                if self.is_treatment_to_be_executed(treatment_status):
                    treatments.append(self.build_treatment_for_file(perimeter_code, treatment_status['file']))
        return treatments

    """
    Lancement des traitements necessaires
    param perimeter_code: le périmètre pour lequel lister les sources à récupérer
    """

    def launch_treatments_for_perimeter(self, perimeter_code: str):
        start_time = time.time()
        log_message(perimeter_code, "launching treatments")
        if not self._treatment_file_manager.get_token(perimeter_code, start_time, ScheduleService.TOKEN_LIFE):
            log_message(perimeter_code, "locked for this perimeter")
            return {"status": "ko", "error": "locked"}

        self.__populate_tasks_to_execute__(perimeter_code)

        ordered_treatments_to_launch = [
            TreatmentFileManager.DOCS, TreatmentFileManager.GET_DOC,
            TreatmentFileManager.REMOVE_DOC,
            TreatmentFileManager.LIST,
            TreatmentFileManager.GET_LIST
        ]

        method_by_treatment_type = {
            TreatmentFileManager.DOCS: self._document_service.embedd_document,
            TreatmentFileManager.REMOVE_DOC: self._document_service.remove_documents,
            TreatmentFileManager.GET_DOC: self._loader_service.get_document,
            TreatmentFileManager.LIST: self._document_service.compare_document_list,
            TreatmentFileManager.GET_LIST: self._loader_service.get_document_list
        }

        total_nb_treatments = 0

        for treatment in ordered_treatments_to_launch:
            nb_treatments = self.__launch_single_treatment_type__(start_time, treatment,
                                                                  method_by_treatment_type[treatment], perimeter_code)
            log_treatments_launched(perimeter_code, treatment, nb_treatments)
            total_nb_treatments += nb_treatments

        if total_nb_treatments == 0 and time.time() - start_time < ScheduleService.MAX_EXECUTION_TIME:
            # no treatments, we need to reload sources and to purge old treatments
            log_message(perimeter_code, "reloading sources")
            self._sources_service.load_sources(perimeter_code)
            log_message(perimeter_code, "purging old treatments ")
            self._treatment_file_manager.purge_old_treatments_files(perimeter_code, datetime.now())

        self._treatment_file_manager.release_token(perimeter_code)
        return {"status": "ok", "error": ""}

    def __populate_tasks_to_execute__(self, perimeter_code: str):
        load_date_list = self._treatment_file_manager.get_load_dates(perimeter_code, datetime.now())
        load_date_list.sort(reverse=True)
        self.tasks_to_execute = {}
        already_processed = []
        for load_date in load_date_list:
            treatments_files = self._treatment_file_manager.get_treatments_files(perimeter_code, load_date)
            for treatment_file, treatment_status in treatments_files.items():
                if self.is_treatment_to_be_executed(treatment_status):
                    self.__add_treatment_to_execute__(already_processed, treatment_status)

    def __add_treatment_to_execute__(self, already_processed, treatment_status):
        treatment_file = treatment_status["file"]
        [_, _, _, file_type, file_sig] = self._treatment_file_manager.extract_info_from_file_name(treatment_file)
        # filling dict tasks which contain for each file_type the list of files to manage
        if file_sig not in already_processed:
            if file_type not in self.tasks_to_execute.keys():
                self.tasks_to_execute[file_type] = []
            self.tasks_to_execute[file_type].append(treatment_file)
            already_processed.append(file_sig)
        else:
            self._treatment_file_manager.set_done(treatment_file)

    """
    Launch a type of treatment (all embeddings or all remove file)
    """

    def __launch_single_treatment_type__(self, start_time, file_type, function_to_call, perimeter_code):
        nb_treatments = 0
        if file_type not in self.tasks_to_execute.keys():
            return nb_treatments
        for treatment_file in self.tasks_to_execute[file_type]:
            if time.time() - start_time < ScheduleService.MAX_EXECUTION_TIME:
                url = self.get_treatment_url(file_type, perimeter_code)
                log_action(perimeter_code, url, treatment_file)
                try:
                    function_to_call(perimeter_code, treatment_file)
                    nb_treatments += 1
                except Exception as e:
                    log_exception(perimeter_code, url, treatment_file, e)
        return nb_treatments

    def build_treatment_for_file(self, perimeter_code: str, treatment_file: str) -> TreatmentBean:
        [_, _, _, file_type, _] = \
            self._treatment_file_manager.extract_info_from_file_name(treatment_file)
        treatment_url = self.get_treatment_url(file_type, perimeter_code)
        param_key = self.PARAM_KEY_BY_TYPE[file_type]
        return TreatmentBean(
            file_type=file_type,
            url=treatment_url,
            params={param_key: treatment_file}
        )

    def get_treatment_url(self, file_type: str, perimeter_code: str) -> str:
        url_template = self.URL_BY_TYPE[file_type]
        return url_template.replace('{perimeter_code}', perimeter_code)

    @staticmethod
    def is_treatment_to_be_executed(treatment_status):
        return ("file" in treatment_status.keys()
                and TreatmentFileManager.FILE_STATUS_IN_PROGRESS not in treatment_status.keys()
                and TreatmentFileManager.FILE_STATUS_DONE not in treatment_status.keys()
                and TreatmentFileManager.FILE_STATUS_IN_ERROR not in treatment_status.keys()
                )
