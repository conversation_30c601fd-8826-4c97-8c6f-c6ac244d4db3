# Documentation du Chargeur SharePoint

## Vue d'ensemble

Le **SharePoint Loader** est un composant du système Kbot Load Scheduler qui permet de récupérer et charger des documents depuis des sites SharePoint vers un stockage cloud (Google Cloud Storage). Il fait partie du module de chargement de données et implémente l'interface `AbstractLoader`.

## Architecture

### Classes principales

1. **`SharepointLoader`** : Classe principale qui orchestrie le chargement des documents
2. **`SharepointClient`** : Client pour interagir avec l'API REST SharePoint
3. **`SharepointCredentials`** : Gestionnaire d'authentification avec SharePoint

## Fonctionnalités principales

### 1. Découverte de documents (`get_document_list`)

Le chargeur peut parcourir récursivement une structure de dossiers SharePoint pour identifier tous les documents disponibles.

**Processus :**
- Se connecte au site SharePoint spécifié dans la configuration de la source
- Navigue vers le répertoire relatif configuré (`relative_directory`)
- Parcourt récursivement tous les sous-dossiers
- Collecte les métadonnées de tous les fichiers trouvés

**Données d'entrée :**
- `source` (`SourceBean`) : Objet contenant la configuration de la source SharePoint

**Données de sortie :**
- `List[DocumentBean]` : Liste des documents trouvés, chaque `DocumentBean` contient :
  - `id` : Identifiant unique au format `{domain_code}|{source_code}|{file_id}`
  - `name` : Chemin relatif du fichier dans SharePoint (ex: `/sites/EquipeIA/Documents partages/rapport.pdf`)
  - `path` : URL complète d'accès au fichier
  - `modification_time` : Date de dernière modification (objet `datetime`)

**Configuration requise :**
```python
source_config = {
    "site_name": "NomDuSite",
    "relative_directory": "Documents partages/Dossier"
}
```

### 2. Téléchargement de documents (`get_document`)

Le chargeur peut télécharger des documents individuels depuis SharePoint vers GCS (Google Cloud Storage).

**Processus :**
1. Authentification auprès de SharePoint
2. Récupération des métadonnées du fichier
3. Téléchargement du contenu binaire
4. Stockage dans GCS avec un nom de fichier nettoyé
5. Extraction et retour des métadonnées enrichies

**Données d'entrée :**
- `source` (`SourceBean`) : Configuration de la source SharePoint
- `document` (`DocumentBean`) : Document à télécharger (issu de `get_document_list`)
- `output_path` (`str`) : Chemin de destination dans GCS (ex: `"gs://bucket/documents/"`)

**Données de sortie :**
- `Dict[str, Any]` : Dictionnaire de métadonnées contenant :
  - `document_id` : Identifiant unique du document
  - `document_name` : Nom/chemin original du document
  - `source_path` : URL source dans SharePoint
  - `location` : Chemin de stockage final dans GCS
  - `creationDate` : Date de création du fichier
  - `modificationDate` : Date de dernière modification

**Métadonnées extraites :**
- `document_id` : Identifiant unique du document
- `document_name` : Nom complet du document
- `source_path` : URL source dans SharePoint
- `location` : Chemin de stockage dans GCS
- `creationDate` : Date de création
- `modificationDate` : Date de dernière modification

### 3. Modes d'identification des documents

Le système supporte deux modes d'identification des fichiers, configurables via la variable d'environnement `SHAREPOINT_IN_DOC_ID_USE_PROPERTY` :

#### Mode `UniqueId` (recommandé)
- Utilise l'identifiant unique GUID de SharePoint
- Plus stable, ne change pas lors de déplacements de fichiers
- Format d'ID : `domaine|source|guid-unique`

#### Mode `ServerRelativeUrl`
- Utilise le chemin relatif du serveur
- Plus lisible mais peut changer si le fichier est déplacé
- Format d'ID : `domaine|source|/sites/site/chemin/fichier.ext`

## Configuration

### SourceBean - Paramètres détaillés

La classe `SourceBean` représente une source de données SharePoint avec les paramètres suivants :

#### **Champs d'identification obligatoires :**
- **`id`** (`int`) : Identifiant unique numérique (généré par la base de données)
- **`code`** (`str`) : Code unique de la source dans le domaine (ex: `"sharepoint_docs"`)
- **`label`** (`str`) : Nom descriptif pour l'affichage (ex: `"Documents SharePoint IA"`)
- **`src_type`** (`str`) : Type de loader, doit être `"sharepoint"`

#### **Champs organisationnels :**
- **`domain_code`** (`str`) : Identifiant du domaine/département (ex: `"ingenierie"`)
- **`perimeter_code`** (`str`) : Code du périmètre de sécurité (ex: `"production"`)

#### **Champs de planification :**
- **`last_load_time`** (`int`) : Timestamp Unix du dernier chargement
- **`load_interval`** (`int`) : Intervalle en heures entre les chargements automatiques
- **`force_embedding`** (`bool`) : Force le recalcul des embeddings (optionnel, défaut: `False`)

#### **Configuration spécifique SharePoint :**
- **`configuration`** (`str`) : Chaîne JSON contenant les paramètres SharePoint :
  ```json
  {
    "site_name": "NomDuSiteSharePoint",
    "relative_directory": "Documents partages/Dossier/Cible"
  }
  ```

#### **Méthodes utiles de SourceBean :**
- `parse_configuration()` : Convertit la configuration JSON en dictionnaire Python
- `get_expected_conf(conf_name)` : Récupère un champ spécifique de la configuration

**Exemple complet :**
```python
source = SourceBean(
    id=1,
    code="sharepoint_docs",
    label="Documents SharePoint IA",
    src_type="sharepoint",
    configuration=json.dumps({
        "site_name": "EquipeIA",
        "relative_directory": "Documents partages/Knowledge Bot"
    }),
    last_load_time=1726156483,
    load_interval=24,
    domain_code="ingenierie",
    perimeter_code="production",
    force_embedding=False
)

# Utilisation
site_name = source.get_expected_conf("site_name")  # Retourne "EquipeIA"
config_dict = source.parse_configuration()  # Retourne le dict complet
```

### Variables d'environnement

```bash
# URL du service SharePoint
URL_SERVICE_SHAREPOINT=https://votreorganisation.sharepoint.com

# Mode d'identification des documents
SHAREPOINT_IN_DOC_ID_USE_PROPERTY=UniqueId  # ou ServerRelativeUrl
```

### Configuration de la source

```python
source = SourceBean(
    domain_code="domaine",
    code="source_code",
    perimeter_code="perimetre",
    config={
        "site_name": "NomDuSiteSharePoint",
        "relative_directory": "Documents partages/Dossier/Cible"
    }
)
```

### Configuration d'authentification

Le système utilise l'authentification par certificat client :

```json
{
    "tenant_id": "id-du-tenant-azure",
    "client_id": "id-de-l-application",
    "certificate_thumbprint": "empreinte-du-certificat",
    "password": "mot-de-passe-certificat"
}
```

## Exemples d'utilisation

### Exemple 1 : Lister les documents

```python
# Configuration
loader = SharepointLoader(config_with_secret)
source = SourceBean(
    domain_code="mondomaine",
    code="masource",
    config={
        "site_name": "EquipeIA",
        "relative_directory": "Documents partages/Knowledge Bot"
    }
)

# Récupération de la liste des documents
documents = loader.get_document_list(source)
print(f"Trouvé {len(documents)} documents")

for doc in documents[:5]:  # Affiche les 5 premiers
    print(f"- {doc.name} (ID: {doc.id})")
```

### Exemple 2 : Télécharger un document

```python
# Document à télécharger
document = DocumentBean(
    id="domaine|source|af7b286c-027b-4a20-b9b7-02b752b87b53",
    name="/sites/EquipeIA/Documents partages/rapport.pdf",
    path="gs://bucket/documents",
    modification_time=datetime.now()
)

# Téléchargement
metadata = loader.get_document(
    source=source,
    document=document,
    output_path="gs://bucket/documents/"
)

print(f"Document sauvegardé : {metadata['location']}")
print(f"Date de création : {metadata['creationDate']}")
print(f"Date de modification : {metadata['modificationDate']}")
```

## Gestion des erreurs

### Types d'erreurs courants

1. **`SharepointException`** : Erreurs d'API SharePoint
   - Code 401 : Problème d'authentification
   - Code 404 : Fichier ou dossier non trouvé
   - Code 403 : Permissions insuffisantes

2. **`LoaderException`** : Erreurs du chargeur
   - Échec de création du fichier en destination
   - Problème de configuration de la source

### Exemple de gestion d'erreur

```python
try:
    documents = loader.get_document_list(source)
except LoaderException as e:
    print(f"Erreur du chargeur : {e}")
except Exception as e:
    print(f"Erreur inattendue : {e}")
```

## Optimisations et bonnes pratiques

### 1. Structure des identifiants de documents

Les identifiants suivent le format : `{domain_code}|{source_code}|{file_identifier}`

Cela permet :
- Une identification unique cross-source
- La traçabilité de l'origine du document
- La gestion des collisions de noms

### 2. Nettoyage des noms de fichiers

Le système nettoie automatiquement les noms de fichiers pour le stockage :
- Remplacement des caractères spéciaux
- Gestion des chemins longs
- Évitement des conflits de nommage

### 3. Métadonnées enrichies (en développement)

Le système prépare l'extraction de métadonnées de relation basées sur la structure des dossiers :

```python
# Métadonnées extraites de la structure
{
    "folder_path": "produits/installation",
    "parent_folder": "installation",
    "folder_hierarchy": ["produits", "installation"],
    "file_base_name": "guide-installation",
    "file_extension": "pdf",
    "depth_level": 2,
    "folder_group_id": "/sites/mysite/docs/produits/installation",
    "document_family": "guide"
}
```

## Tests et validation

Le système inclut une suite de tests complète qui valide :

1. **Navigation dans les dossiers** : Test de parcours récursif
2. **Téléchargement de documents** : Validation du processus complet
3. **Modes d'identification** : Tests pour `UniqueId` et `ServerRelativeUrl`
4. **Gestion des erreurs** : Validation de la robustesse
5. **Intégration GCS** : Tests de stockage cloud

### Exécution des tests

```bash
# Tests spécifiques SharePoint
python -m pytest tests/loader/sharepoint/test_sharepoint_loader.py -v

# Test particulier avec sortie détaillée
python -m pytest tests/loader/sharepoint/test_sharepoint_loader.py::TestSharepointLoader::test_get_document_list_with_unique_id -v -s
```

## Sécurité

### Authentification
- Utilisation de certificats clients pour l'authentification
- Gestion sécurisée des clés privées via le gestionnaire de secrets
- Tokens d'accès temporaires avec renouvellement automatique

### Permissions
- Lecture seule sur les sites SharePoint
- Accès limité aux dossiers configurés
- Respect des permissions SharePoint existantes

## Monitoring et logs

Le chargeur génère des logs détaillés pour :
- Suivi des opérations de découverte
- Traçabilité des téléchargements
- Détection des erreurs d'API
- Métriques de performance

## Limitations actuelles

1. **Lecture seule** : Pas de modification des documents SharePoint
2. **Authentification par certificat uniquement** : Pas de support OAuth interactif
3. **Pas de versioning** : Seule la version courante est récupérée
4. **Limitation de taille** : Dépendante des limites de l'API SharePoint REST

## Évolutions prévues

1. **Métadonnées de relation** : Exploitation de la structure des dossiers
2. **Cache intelligent** : Éviter les téléchargements redondants
3. **Support delta** : Synchronisation incrémentale
4. **Gestion des permissions** : Mapping des droits SharePoint

## Workflow de Sauvegarde et d'Utilisation des Documents

### Vue d'ensemble du processus

Le système utilise une architecture découplée pour gérer efficacement les listes de documents, depuis leur découverte jusqu'à leur traitement individuel.

```mermaid
graph TD
    A["get_document_list génère List[DocumentBean]"] --> B[write_document_list sauvegarde list.json sur GCS]
    B --> C[DocumentService compare avec base indexation]
    C --> D[write_document_get_file génère getdoc.json pour chaque doc]
    D --> E[get_document lit getdoc.json spécifique]
    E --> F[read_document_get_file reconstitue SourceBean + DocumentBean]
    F --> G[Télécharge et traite le document SharePoint]
    G --> H[write_document_metadata sauvegarde métadonnées]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#e8f5e8
    style H fill:#fff9c4

    subgraph "Phase 1: Découverte"
        A
        B
    end

    subgraph "Phase 2: Comparaison"
        C
        D
    end

    subgraph "Phase 3: Traitement Individuel"
        E
        F
        G
        H
    end
```

#### 1. Phase de découverte (`get_document_list`)

```python
# Génération de la liste complète
documents = loader.get_document_list(source)  # Retourne List[DocumentBean]

# Sauvegarde immédiate dans GCS
treatment_file_manager.write_document_list(load_date, source, documents, "list")
```

**Fichier généré :** `gs://bucket/{load_date}/{domain_code}/{source_code}/list/list.json`

**Structure du fichier `list.json` :**
```json
{
  "source": {
    "perimeter_code": "ACME",
    "domain_code": "sharepoint",
    "code": "docs",
    "site_name": "EquipeIA",
    "relative_directory": "Documents partages/Test"
  },
  "documents": [
    {
      "id": "acme|sharepoint_docs|af7b286c-027b-4a20-b9b7-02b752b87b53",
      "name": "/sites/EquipeIA/Documents partages/rapport.pdf",
      "path": "https://orange0.sharepoint.com/...",
      "modification_time": "2024-01-19T16:08:37Z"
    }
    // ... autres documents
  ]
}
```

#### 2. Phase de comparaison et génération des tâches individuelles

Le `DocumentService` compare la liste avec la base d'indexation existante et génère un fichier `getdoc.json` **pour chaque document à traiter** :

```python
# Génération d'un fichier par document à traiter
for document in documents_to_process:
    treatment_file_manager.write_document_get_file(load_date, source, document)
```

**Fichier généré :** `gs://bucket/{load_date}/{domain_code}/{source_code}/getdoc/{nom_fichier_nettoye}.getdoc.json`

**Structure du fichier `getdoc.json` :**
```json
{
  "source": {
    "perimeter_code": "ACME",
    "domain_code": "sharepoint",
    "code": "docs",
    "site_name": "EquipeIA",
    "relative_directory": "Documents partages/Test"
  },
  "document": {
    "id": "acme|sharepoint_docs|af7b286c-027b-4a20-b9b7-02b752b87b53",
    "name": "/sites/EquipeIA/Documents partages/rapport.pdf",
    "path": "https://orange0.sharepoint.com/...",
    "modification_time": "2024-01-19T16:08:37Z"
  }
}
```

#### 3. Phase de traitement individuel (`get_document`)

```python
# Lecture du fichier individuel
get_doc_data = TreatmentFileManager.read_document_get_file(getdoc_file)
source = get_doc_data["source"]      # SourceBean reconstitué
document = get_doc_data["document"]  # DocumentBean reconstitué

# Traitement du document
metadata = loader.get_document(source, document, output_path)
```

## Orchestration et déclenchement par Cloud Scheduler

### Workflow complet d'orchestration

`get_document_list` et `get_document` ne sont pas appelés directement mais font partie d'un workflow orchestré par **Google Cloud Scheduler** qui coordonne l'ensemble du processus de chargement.

#### 1. Déclenchement initial par Cloud Scheduler

```mermaid
sequenceDiagram
    participant CS as "☁️ Cloud Scheduler"
    participant API as "🚀 kbot-load-scheduler API"
    participant SS as "⚙️ ScheduleService"
    participant DS as "📊 DocumentService"
    participant LS as "📤 LoaderService"
    participant GCS as "📦 GCS"

    CS->>API: POST /schedule/treatments/{perimeter}/launch
    API->>SS: launch_treatments_for_perimeter()
    SS->>GCS: Scan for treatment files (.json without .done)
    GCS-->>SS: List of pending tasks

    Note over SS: Process tasks in priority order:<br/>GET_LIST → LIST → GET_DOC → DOCS → REMOVE_DOC

    SS->>LS: get_document_list() for each getlist.json
    LS-->>GCS: Creates list.json files

    SS->>DS: compare_document_list() for each list.json
    DS-->>GCS: Creates getdoc.json + removedoc.json files

    SS->>LS: get_document() for each getdoc.json
    LS-->>GCS: Creates .metadata.json files

    SS->>DS: embedd_document() for each .metadata.json
    SS->>DS: remove_documents() for each removedoc.json
```

#### 2. Endpoints API déclenchés automatiquement

Le `ScheduleService` appelle automatiquement ces endpoints dans l'ordre :

| Ordre | Endpoint | Route Handler | Service + Méthode | Entrée | Sortie |
|-------|----------|---------------|-------------------|---------|---------|
| 1 | `POST /loader/list/{perimeter}` | `get_document_list()` | `LoaderService.get_document_list()` | `getlist.json` | `list.json` |
| 2 | `POST /document/compare/{perimeter}` | `compare_list()` | `DocumentService.compare_document_list()` | `list.json` | `getdoc.json` + `removedoc.json` |
| 3 | `POST /loader/document/{perimeter}` | `get_document()` | `LoaderService.get_document()` | `getdoc.json` | `.metadata.json` |
| 4 | `POST /document/embedd/{perimeter}` | `embedd_document()` | `DocumentService.embedd_document()` | `.metadata.json` | Indexation vectorielle |
| 5 | `POST /document/remove/{perimeter}` | `remove_documents()` | `DocumentService.remove_documents()` | `removedoc.json` | Suppression vectorielle |

**⚠️ Important** : Distinction entre routes API et méthodes de service :
- Les **routes** (dans `/route/`) sont les fonctions FastAPI qui reçoivent les requêtes HTTP
- Les **méthodes de service** (dans `/service/`) contiennent la logique métier réelle

```python
# Exemple pour l'étape de comparaison :
# 1. Route API (document_routes.py)
@router.post("/document/compare/{perimeter_code}")
async def compare_list(perimeter_code, repo_document_list_file_bean, document_service):
    return document_service.compare_document_list(perimeter_code, repo_document_list_file)

# 2. Méthode de service (document_service.py)
def compare_document_list(self, perimeter_code: str, document_list_file: str):
    # Logique métier réelle de comparaison
```

#### 3. Rôle central du DocumentService

Le `DocumentService` intervient à **deux moments clés** dans l'orchestration :

##### Phase 2 : Comparaison (`/document/compare/{perimeter}`)

**Route API :** `compare_list()` → **Méthode service :** `compare_document_list()`

```python
# 1. Route API (document_routes.py)
@router.post("/document/compare/{perimeter_code}")
async def compare_list(perimeter_code, repo_document_list_file_bean, document_service):
    # Délègue au service
    return document_service.compare_document_list(perimeter_code, repo_document_list_file)

# 2. Méthode du service (document_service.py) - Logique métier
def compare_document_list(self, perimeter_code: str, document_list_file: str):
    # 1. Lit le fichier list.json généré par get_document_list
    document_list = self._treatment_file_manager.read_document_list(document_list_file)
    source = document_list.get("source")
    repo_documents = document_list.get("documents")  # Documents SharePoint

    # 2. Interroge la base vectorielle existante
    embedded_documents = self._kbot_embedding_api.get_document_list(perimeter_code, domain_code, source_code)

    # 3. Compare les deux listes
    comparator = DocumentListComparator()
    comparator.compare(embedded_documents, repo_documents, source.force_embedding)

    # 4. Génère les fichiers de tâches individuelles
    for document in comparator.get_documents_to_get():
        self._treatment_file_manager.write_document_get_file(load_date, source, document)

    # 5. Génère le fichier de suppression
    self._treatment_file_manager.write_document_list(
        load_date, source, comparator.get_documents_to_remove(), "removedoc"
    )
```
    embedded_documents = self._kbot_embedding_api.get_document_list(perimeter_code, domain_code, source_code)

    # 3. Compare les deux listes
    comparator = DocumentListComparator()
    comparator.compare(embedded_documents, repo_documents, source.force_embedding)

    # 4. Génère les fichiers de tâches individuelles
    for document in comparator.get_documents_to_get():
        self._treatment_file_manager.write_document_get_file(load_date, source, document)

    # 5. Génère le fichier de suppression
    self._treatment_file_manager.write_document_list(
        load_date, source, comparator.get_documents_to_remove(), "removedoc"
    )
```

##### Phase 4-5 : Indexation et suppression

**Routes API :** `embedd_document()` et `remove_documents()` → **Méthodes service :** `embedd_document()` et `remove_documents()`

```python
# Phase 4: Indexation vectorielle
# Route: embedd_document() → Service: embedd_document()
def embedd_document(self, perimeter_code: str, embedd_document_metadata_file: str):
    metadata_json = self._treatment_file_manager.read_document_metadata(embedd_document_metadata_file)
    result = self._kbot_embedding_api.embedd_document(perimeter_code, embedd_document_metadata_file)

# Phase 5: Suppression vectorielle
# Route: remove_documents() → Service: remove_documents()
def remove_documents(self, perimeter_code: str, document_list_file: str):
    document_list = self._treatment_file_manager.read_document_list(document_list_file)
    for document in document_list.get("documents"):
        self._kbot_embedding_api.remove_document(perimeter_code, document)
```

#### 4. Architecture découplée basée sur fichiers

Le système utilise GCS comme **bus de messages asynchrone** :

```
gs://bucket/{load_date}/{domain}/{source}/
├── getlist/
│   └── getlist.json          # Configuration source (entrée)
├── list/
│   └── list.json             # Liste documents découverts
├── getdoc/
│   ├── doc1.getdoc.json      # Tâche individuelle document 1
│   ├── doc2.getdoc.json      # Tâche individuelle document 2
│   └── ...
├── docs/
│   ├── doc1.pdf              # Contenu téléchargé
│   ├── doc1.pdf.metadata.json # Métadonnées
│   └── ...
└── removedoc/
    └── removedoc.json        # Documents à supprimer
```

#### 5. Gestion d'état et reprise sur erreur

Chaque fichier de tâche peut avoir les états suivants :
- `.json` : Tâche en attente
- `.inprogress` : Tâche en cours d'exécution
- `.done` : Tâche terminée avec succès
- `.error` : Tâche échouée

```python
# ScheduleService scanne uniquement les tâches prêtes
def is_treatment_to_be_executed(treatment_status):
    return (
        "file" in treatment_status.keys() and
        FILE_STATUS_IN_PROGRESS not in treatment_status.keys() and
        FILE_STATUS_DONE not in treatment_status.keys() and
        FILE_STATUS_IN_ERROR not in treatment_status.keys()
    )
```

#### 6. Configuration Cloud Scheduler

```yaml
# Déclenchement périodique (ex: toutes les heures)
schedule: "0 */1 * * *"
target:
  httpTarget:
    uri: "https://kbot-load-scheduler-prod.run.app/schedule/treatments/{perimeter}/launch"
    httpMethod: POST
```

### Avantages de cette orchestration

1. **Déclaratif** : Les tâches sont des fichiers JSON, pas des appels directs
2. **Résilient** : Chaque étape peut être rejouée indépendamment
3. **Scalable** : Traitement parallèle possible (un fichier = une tâche)
4. **Auditable** : Historique complet dans GCS
5. **Découplé** : `get_document_list`, `DocumentService` et `get_document` fonctionnent indépendamment

Cette architecture permet à Cloud Scheduler de coordonner des workflows complexes tout en gardant chaque composant (SharePoint Loader, DocumentService, etc.) simple et focalisé sur sa responsabilité spécifique.

## Gestion de la Mémoire et Performance

### Risques identifiés

Pour de gros volumes de documents, le système peut rencontrer des problèmes de mémoire :

- **SharePoint volumineux** : Sites avec des milliers de fichiers
- **Memory overflow** : Notamment sur Cloud Run (limite typique 2GB)
- **Double allocation** : Liste originale + liste sérialisée en mémoire simultanément

### Stratégies de mitigation

#### 1. Limitations par configuration

```python
# Variables d'environnement recommandées pour gros volumes
SHAREPOINT_MAX_DOCUMENTS_PER_BATCH=500
SHAREPOINT_ENABLE_PAGINATION=true
SHAREPOINT_MEMORY_OPTIMIZATION=true
```

#### 2. Filtrage agressif

```python
source_config = {
    "site_name": "EquipeIA",
    "relative_directory": "Documents partages/Dossier_Specifique",  # Chemin précis
    "file_extensions_filter": ["pdf", "docx"],  # Limiter les types
    "max_file_size_mb": 50,  # Limiter la taille
    "modified_since_days": 30  # Filtre temporel
}
```

#### 3. Traitement par batch (recommandé pour gros volumes)

Si vous devez traiter de très gros volumes, voici les patterns recommandés :

```python
# Pattern de pagination pour get_document_list
def get_document_list_paginated(self, source: SourceBean, batch_size: int = 500):
    """Version paginée pour éviter les problèmes mémoire."""
    all_documents = []
    skip = 0

    while True:
        batch_documents = self._get_documents_batch(source, skip, batch_size)
        if not batch_documents:
            break

        all_documents.extend(batch_documents)
        skip += batch_size

        # Libérer la mémoire périodiquement
        if len(all_documents) >= 2000:
            # Sauvegarder et vider
            self._save_batch_and_clear(all_documents, source)
            all_documents = []

    return all_documents
```

#### 4. Monitoring recommandé

```python
# Métriques à surveiller en production
monitoring_metrics = {
    "memory_usage_mb": "< 1500 MB",
    "documents_per_batch": "< 1000",
    "processing_time_per_1000_docs": "< 5 minutes",
    "gc_frequency": "Surveiller les garbage collections",
    "heap_size": "< 80% de la limite Cloud Run"
}
```

### Optimisations Cloud Run spécifiques

```yaml
# Configuration Cloud Run recommandée pour gros volumes
apiVersion: serving.knative.dev/v1
kind: Service
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/memory: "4Gi"        # Augmenter si nécessaire
        run.googleapis.com/cpu: "2"
        run.googleapis.com/timeout: "1800s"     # 30 minutes pour gros volumes
    spec:
      containerConcurrency: 1                   # Une requête à la fois
      containers:
      - env:
        - name: SHAREPOINT_MAX_DOCUMENTS_PER_BATCH
          value: "500"
        - name: SHAREPOINT_MEMORY_OPTIMIZATION
          value: "true"
```

### Signaux d'alerte

Surveillez ces indicateurs pour détecter les problèmes de mémoire :

1. **Logs d'erreur** : `OutOfMemoryError`, `MemoryError`
2. **Timeouts fréquents** : Cloud Run qui dépasse les 15-30 minutes
3. **Garbage Collection** : GC trop fréquents dans les logs
4. **Dégradation progressive** : Ralentissement au fil du traitement

### Solutions d'urgence

Si vous rencontrez des problèmes mémoire en production :

```python
# Solution immédiate : réduire drastiquement la taille des batches
SHAREPOINT_MAX_DOCUMENTS_PER_BATCH=100
SHAREPOINT_FORCE_SEQUENTIAL_PROCESSING=true

# Solution temporaire : filtrage par date très restrictif
source_config = {
    "modified_since_days": 7,  # Très récent seulement
    "max_documents": 200       # Hard limit
}
```

### Roadmap d'amélioration

Les développements futurs pourraient inclure :

1. **Streaming architecture** : Traitement des documents au fur et à mesure de leur découverte
2. **Pagination native** : Support de la pagination SharePoint API
3. **Memory pooling** : Réutilisation des objets pour réduire l'allocation
4. **Lazy loading** : Chargement à la demande des métadonnées
5. **Compression** : Compression des fichiers JSON pour économiser l'espace GCS
