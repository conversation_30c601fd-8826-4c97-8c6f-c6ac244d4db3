"""
Main search strategy based on Confluence Query Language (CQL),
including query construction, resilient execution, fallback strategies,
and result filtering.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Generator

from ..config.schema import ConfluenceConfig
from ..utils.circuit_breaker import CircuitBreaker
from ..utils.retry_factory import create_search_retryer

class CqlSearch:
    """Executes CQL searches to find Confluence content."""

    def __init__(self, client, circuit_breaker: CircuitBreaker):
        """
        Initialize the CQL searcher.

        Args:
            client: The Confluence client to execute queries.
            circuit_breaker: An instance of the circuit breaker for resilience.
        """
        self.client = client
        self.circuit_breaker = circuit_breaker


    def _paginate_search_generator(self, cql: str, config: ConfluenceConfig, expand: str) -> Generator[Dict, None, None]:
        """
        A generator that handles paginated CQL searches resiliently.
        It yields each result dictionary as it is retrieved.
        """
        retryer = create_search_retryer(config)
        search_method = self.client.search_content

        start = 0
        page_size = 100
        max_results = config.basic.max_results or float('inf')

        while start < max_results:
            # Determine the number of results to fetch in the current request
            current_limit = min(page_size, max_results - start) if max_results != float('inf') else page_size
            if current_limit <= 0:
                break

            try:
                # Use the circuit breaker and retryer to make the API call robust
                raw_results = self.circuit_breaker.call(
                    retryer,
                    search_method,
                    cql=cql,
                    limit=current_limit,
                    start=start,
                    expand=expand
                )
            except Exception as e:
                logging.error(f"CQL search execution failed for '{cql}' (start={start}): {e}", exc_info=True)
                raise

            if not raw_results:
                break  # No more results

            yield from raw_results

            if len(raw_results) < current_limit:
                break  # This was the last page

            start += len(raw_results)

    def search_pages_in_space(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        primary_cql = self._build_cql_query(space_key, config)
        # --- REFACTOR --- Changed log message to English for consistency
        results = self._execute_search(primary_cql, config)
        logging.info(f"Primary search for space '{space_key}' returned {len(results)} pages.")

        if not results and not config.filtering.custom_cql:
            logging.warning(f"Primary search for '{space_key}' returned no results. Attempting fallback strategies.")
            results = self._try_fallback_searches(space_key, config)

        return results

    def search_all_pages_in_space(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        """
        Search all pages in a space at once, using the pagination generator.
        """
        if (config.filtering.labels and
            config.basic.include_child_pages and
            config.basic.child_page_depth > 0):
            logging.info(f"Using child-aware label filtering for space '{space_key}'")
            return self._search_with_child_aware_label_filtering(space_key, config)

        cql = self._build_cql_query(space_key, config)
        logging.info(f"CQL for all pages (hierarchy) in space '{space_key}': {cql}")

        # --- REFACTOR ---
        # The complex pagination loop is replaced by a single call to the generator.
        # This is cleaner and reuses the robust pagination logic.
        expand_for_hierarchy = "version,space,ancestors,metadata.labels"
        all_results = list(self._paginate_search_generator(cql, config, expand_for_hierarchy))

        logging.info(f"All pages search for hierarchy in space '{space_key}' returned {len(all_results)} pages.")
        if not all_results:
            logging.warning(f"No pages found in space '{space_key}' for hierarchy construction.")

        # The result from the generator is already a list of page dicts.
        # `_filter_and_normalize_pages` is still useful to handle variations in search result structure.
        return self._filter_and_normalize_pages(all_results)

    def search_child_pages(self, parent_page_id: str, config: ConfluenceConfig) -> List[Dict]:
        """
        Search for direct child pages of a parent page.

        Args:
            parent_page_id: The parent page ID.
            config: The source configuration.

        Returns:
            A list of dictionaries representing the child pages.
        """
        try:
            child_cql = f"parent = {parent_page_id} AND type = page"
            if config.filtering.last_modified_days:
                cutoff_date = datetime.now() - timedelta(days=config.filtering.last_modified_days)
                child_cql += f" AND lastModified >= '{cutoff_date.strftime('%Y-%m-%d')}'"

            # For children, we do not always expect a result, so we ignore warnings if empty.
            return self._execute_search(child_cql, config, ignore_empty_warning=True)
        except Exception as e:
            logging.error(f"Failed to search for child pages for parent {parent_page_id}: {e}")
            return []

    def _execute_search(self, cql: str, config: ConfluenceConfig, ignore_empty_warning: bool = False) -> List[Dict]:
        """
        Private method to execute a CQL query resiliently by consuming the pagination generator.
        """
        if logging.getLogger().isEnabledFor(logging.DEBUG):
            logging.debug("Executing CQL query: %s", cql)

        expand_params = "version,space,_links.webui,body.storage,metadata.labels,ancestors"

        # --- REFACTOR --- Consume the generator to get all results
        all_results = list(self._paginate_search_generator(cql, config, expand_params))

        if not all_results and not ignore_empty_warning:
            logging.warning(f"The CQL query '{cql}' returned no results.")
            return []

        page_results = self._filter_and_normalize_pages(all_results)
        logging.info(
            f"Pagination complete: {len(page_results)} pages kept from {len(all_results)} raw results."
        )
        return page_results

    def _build_cql_query(self, space_key: str, config: ConfluenceConfig) -> str:
        """Builds the CQL query string from configuration parameters."""
        if config.filtering.custom_cql:
            logging.info(f"Using custom CQL for space '{space_key}': {config.filtering.custom_cql}")
            return config.filtering.custom_cql

        cql_parts = [f'space = "{space_key}"']

        if config.filtering.last_modified_days is not None:
            cutoff_date = datetime.now() - timedelta(days=config.filtering.last_modified_days)
            cql_parts.append(f"lastModified >= '{cutoff_date.strftime('%Y-%m-%d')}'")

        if config.filtering.labels:
            labels_cql = ", ".join(f"'{label.strip()}'" for label in config.filtering.labels)
            cql_parts.append(f"label in ({labels_cql})")

        if config.filtering.exclude_labels:
            exclude_labels_cql = ", ".join(f"'{label.strip()}'" for label in config.filtering.exclude_labels)
            cql_parts.append(f"label not in ({exclude_labels_cql})")

        cql_parts.append("type = page")
        final_cql = " AND ".join(cql_parts)
        logging.info(f"Auto-generated CQL for space '{space_key}': {final_cql}")
        return final_cql

    def _try_fallback_searches(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        """Attempts simpler alternative queries if the main query is empty."""
        logging.info("Fallback strategy 1: Try with a simple CQL (type=page).")
        simple_cql = f'space = "{space_key}" AND type = page'
        results = self._execute_search(simple_cql, config, ignore_empty_warning=True)
        if results:
            logging.info(f"Fallback strategy 1 found {len(results)} pages.")
            return results

        logging.info("Fallback strategy 2: Try with legacy method 'get_space_content'.")
        try:
            # Note: get_space_content may also need pagination, but that depends on the client implementation
            # For now, we'll use it as-is since it's a legacy fallback
            legacy_results = self.client.get_space_content(space_key, expand="version,space,_links.webui")
            if legacy_results:
                filtered = self._filter_and_normalize_pages(legacy_results)
                logging.info(f"Fallback strategy 2 found {len(filtered)} pages.")
                return filtered
        except Exception as e:
            logging.warning(f"Legacy search method failed: {e}")

        return []

    def _filter_and_normalize_pages(self, results: List[Dict]) -> List[Dict]:
        """Filters raw results to keep only pages and normalizes their structure."""
        page_results = []
        for result in results:
            if not isinstance(result, dict):
                continue
            if result.get("type") == "page":
                page_results.append(result)
            elif "content" in result and isinstance(result.get("content"), dict) and result["content"].get("type") == "page":
                page_results.append(self._flatten_search_result(result))
        return page_results

    def _flatten_search_result(self, result: Dict) -> Dict:
        """Flattens a complex search result into a simple page structure."""
        content = result["content"]
        return {
            "id": content.get("id"),
            "type": "page",
            "title": result.get("title") or content.get("title"),
            "status": content.get("status"),
            "_links": content.get("_links", {}),
            "space": content.get("_expandable", {}).get("space", {}),
            "lastModified": result.get("lastModified"),
            "url": result.get("url"),
            "excerpt": result.get("excerpt"),
            "_original_content": content
        }

    def _search_with_child_aware_label_filtering(self, space_key: str, config: ConfluenceConfig) -> List[Dict]:
        """
        Performs a search that includes children of labeled pages, even if children don't have labels.

        Strategy:
        1. First, get all pages with required labels (these are our "seed" pages)
        2. Then, get all pages in the space without label filtering
        3. For each labeled page, include its descendants up to child_page_depth
        """

        # Step 1: Get all pages in space without label filtering
        base_cql_parts = [f'space = "{space_key}"']

        if config.filtering.last_modified_days is not None:
            cutoff_date = datetime.now() - timedelta(days=config.filtering.last_modified_days)
            base_cql_parts.append(f"lastModified >= '{cutoff_date.strftime('%Y-%m-%d')}'")

        if config.filtering.exclude_labels:
            exclude_labels_cql = ", ".join(f"'{label.strip()}'" for label in config.filtering.exclude_labels)
            base_cql_parts.append(f"label not in ({exclude_labels_cql})")

        base_cql_parts.append("type = page")
        base_cql = " AND ".join(base_cql_parts)

        logging.info(f"Child-aware search: Getting all pages with base CQL: {base_cql}")

        expand_for_hierarchy = "version,space,ancestors,metadata.labels"
        all_results = list(self._paginate_search_generator(base_cql, config, expand_for_hierarchy))
        # --- FIN DE LA RECOMMANDATION ---

        # Normalize to pages only
        all_pages = self._filter_and_normalize_pages(all_results)
        logging.info(f"Child-aware search: Found {len(all_pages)} total pages in space")

        # Step 2: Apply child-aware label filtering
        filtered_pages = self._apply_child_aware_label_filter(all_pages, config)
        logging.info(f"Child-aware search: {len(filtered_pages)} pages kept after child-aware label filtering")

        return filtered_pages

    def _apply_child_aware_label_filter(self, all_pages: List[Dict], config: ConfluenceConfig) -> List[Dict]:
        """
        Apply label filtering that includes children of labeled pages.
        """
        from .search_filters import _get_result_labels

        if not config.filtering.labels:
            return all_pages

        # Build page hierarchy map for efficient lookups
        page_by_id = {page.get('id'): page for page in all_pages if page.get('id')}
        children_by_parent = {}

        for page in all_pages:
            page_id = page.get('id')
            ancestors = page.get('ancestors', [])
            if ancestors and page_id:
                # Find immediate parent (last ancestor)
                parent_id = ancestors[-1].get('id') if ancestors else None
                if parent_id:
                    if parent_id not in children_by_parent:
                        children_by_parent[parent_id] = []
                    children_by_parent[parent_id].append(page_id)

        # Find pages that match labels (our seed pages)
        seed_pages = set()
        for page in all_pages:
            page_labels = _get_result_labels(page)
            if any(req_label in page_labels for req_label in config.filtering.labels):
                seed_pages.add(page.get('id'))

        logging.info(f"Found {len(seed_pages)} seed pages with required labels")

        # Include descendants of seed pages up to configured depth
        included_pages = set(seed_pages)

        def include_descendants(page_id: str, current_depth: int):
            if current_depth >= config.basic.child_page_depth:
                return

            child_ids = children_by_parent.get(page_id, [])
            for child_id in child_ids:
                if child_id not in included_pages:
                    included_pages.add(child_id)
                    include_descendants(child_id, current_depth + 1)

        for seed_page_id in seed_pages:
            include_descendants(seed_page_id, 0)

        logging.info(f"Child-aware filtering: {len(included_pages)} total pages included (seed + descendants)")

        # Return only included pages
        return [page for page in all_pages if page.get('id') in included_pages]
