
import logging
import time
import json
import re
import xml.etree.ElementTree as ET
from contextlib import contextmanager
from typing import Optional, Any, Set
from functools import lru_cache
from typing import Dict, List


import requests
from atlassian import Confluence

from kbotloadscheduler.exceptions import (
    ConfluenceClientException,
    ConfluenceAuthenticationError,  # noqa: F401
    ConfluenceNotFoundError,        # noqa: F401
    ConfluenceTimeoutError,
)
from ..config.schema import ConfluenceConfig
from .confluence_credentials import ConfluenceCredentials
from ..utils.retry_factory import create_api_retryer, create_search_retryer


logger = logging.getLogger(__name__)

# ------------------------------------------------------------------
# Security & Debugging Helpers
# ------------------------------------------------------------------

def _is_secret_like(key: str, value: str) -> bool:
    """Check if a header key/value looks like it contains sensitive data"""
    key_lower = key.lower()

    # Known secret header patterns
    secret_patterns = [
        'authorization', 'auth', 'token', 'key', 'secret', 'password',
        'credential', 'bearer', 'basic', 'api-key', 'x-api-key',
        'x-atlassian-token', 'x-auth', 'cookie', 'session'
    ]

    # Check if key matches secret patterns
    if any(pattern in key_lower for pattern in secret_patterns):
        return True

    # Check if value looks like a token/secret (long alphanumeric strings)
    if len(value) > 20 and re.match(r'^[A-Za-z0-9+/=_-]+$', value):
        return True

    return False

def _redact_headers(headers: dict) -> dict:
    """Redact any headers that look like they contain secrets"""
    return {
        k: '***REDACTED***' if _is_secret_like(k, str(v)) else v
        for k, v in headers.items()
    }

def _safe_json_preview(data: Any, max_bytes: int = 200) -> str:
    """Get a safe preview of JSON data, truncated if too long"""
    try:
        if isinstance(data, (dict, list)):
            json_str = json.dumps(data, indent=None, separators=(',', ':'))
        else:
            json_str = str(data)

        if len(json_str) <= max_bytes:
            return json_str
        else:
            return json_str[:max_bytes] + '...[truncated]'
    except Exception:
        return f"<non-serializable {type(data).__name__}>"

# ------------------------------------------------------------------
# Enhanced Exception Classes
# ------------------------------------------------------------------

class EnhancedConfluenceException(Exception):
    """Base exception with debug details attached"""
    def __init__(self, message: str, debug_details: Optional[Dict[str, Any]] = None, **kwargs):
        super().__init__(message)
        self.debug_details = debug_details or {}
        for key, value in kwargs.items():
            setattr(self, key, value)

# ------------------------------------------------------------------
# Caching & Network Helpers
# ------------------------------------------------------------------

@lru_cache(maxsize=512)
def _cached_fetch_attachment(url: str, attachment_id: str, verify_ssl: bool) -> Dict:
    api_url = f"{url}/rest/api/content/{attachment_id}"
    with requests.Session() as s:
        s.verify = verify_ssl
        headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (compatible; KbotConfluenceClient/1.3)"
        }
        resp = s.get(api_url, headers=headers, timeout=30)
        resp.raise_for_status()
        return resp.json()

# ------------------------------------------------------------------
# Main Client
# ------------------------------------------------------------------

class ConfluenceClient:
    def __init__(self, credentials: ConfluenceCredentials, config: ConfluenceConfig):
        if not isinstance(credentials, ConfluenceCredentials) or not credentials.is_valid():
            raise ConfluenceAuthenticationError("A valid ConfluenceCredentials object is required.")

        self.credentials = credentials
        self.config = config
        self.url = self.credentials.url.rstrip("/")
        self.verify_ssl = True
        self.timeout = self.config.file_processing.default_timeout
        self._session: Optional[requests.Session] = None

        self._initialize_confluence_client()
        self._configure_session()

    # -------------------------------
    # Context Management
    # -------------------------------
    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        if self._session:
            self._session.close()
            self._session = None
            logger.debug("Custom requests session closed.")
        if hasattr(self.confluence, '_session') and self.confluence._session:
            self.confluence._session.close()
            logger.debug("Atlassian library session closed.")

        _cached_fetch_attachment.cache_clear()
        logger.debug("Attachment metadata cache cleared.")

    # -------------------------------
    # Internal Setup
    # -------------------------------
    def _initialize_confluence_client(self) -> None:
        kwargs = dict(
            url=self.url,
            timeout=self.timeout,
            verify_ssl=self.verify_ssl,
            cloud=self.credentials.cloud,
        )
        if self.credentials.pat_token:
            self.confluence = Confluence(token=self.credentials.pat_token, **kwargs)
        elif self.credentials.username and self.credentials.api_token:
            self.confluence = Confluence(
                username=self.credentials.username,
                password=self.credentials.api_token,
                **kwargs
            )
        else:
            raise ConfluenceAuthenticationError(
                "No valid authentication method (PAT or user/token) found in credentials."
            )

    def _configure_session(self) -> None:
        if self._session is None:
            self._session = requests.Session()
        self._session.verify = self.verify_ssl
        headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (compatible; KbotConfluenceClient/1.3)"
        }
        headers.update(self.credentials.get_auth_headers())
        self._session.headers.update(headers)

    # -------------------------------
    # Low-level Networking
    # -------------------------------
    @contextmanager
    def _safe_request_context(self, method: str, url: str, **kwargs):
        """Context manager that yields a response or raises with debug info attached."""
        start = time.time()
        debug = self._collect_debug_snapshot(method, url, kwargs)
        kwargs.setdefault('timeout', self.timeout)

        try:
            resp = self._make_authenticated_request(method, url, **kwargs)
            resp.raise_for_status()
            yield resp
        except requests.exceptions.RequestException as exc:
            debug.update(self._enrich_debug_on_error(exc, url, time.time() - start))
            raise self._wrap_exception(exc, url, debug) from exc
        finally:
            if 'resp' in locals() and resp is not None:
                resp.close()

    def _make_authenticated_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make authenticated request with safe header logging"""
        if self._session is None:
            self._configure_session()

        # For debugging: log safe headers only on errors (via exception details)
        return self._session.request(method, url, **kwargs)

    # ---------- helper methods ----------

    def _collect_debug_snapshot(self, method: str, url: str, kwargs: dict) -> Dict[str, Any]:
        """Return a dict with request-level metadata (no response yet)."""
        snap = {
            'method': method,
            'url': url,
            'timeout': kwargs.get('timeout', self.timeout),
            'ssl_verify': self.verify_ssl,
            'confluence_url': self.url,
        }
        if method in ('POST', 'PUT'):
            if kwargs.get('json'):
                snap['request_body'] = _safe_json_preview(kwargs['json'])
            elif kwargs.get('data'):
                snap['request_body'] = str(kwargs['data'])[:200] + ('...' if len(str(kwargs['data'])) > 200 else '')
        if self._session:
            snap['safe_headers'] = _redact_headers(dict(self._session.headers))
        return snap

    def _enrich_debug_on_error(
        self, exc: requests.exceptions.RequestException, url: str, duration: float
    ) -> Dict[str, Any]:
        """Return extra debug info after an exception occurs."""
        details = {"duration": duration, "error_type": type(exc).__name__}

        if isinstance(exc, requests.exceptions.HTTPError):
            resp = exc.response
            if resp is not None:
                details.update(
                    {
                        "status_code": resp.status_code,
                        "reason": resp.reason,
                        "response_headers": _redact_headers(dict(resp.headers)),
                    }
                )
                # small body preview
                try:
                    if resp.content and len(resp.content) < 250:
                        details["response_body"] = resp.text[:250]
                except Exception:
                    pass

        elif isinstance(exc, requests.exceptions.Timeout):
            details["dns_ok"] = self._check_dns_resolution(url)

        elif isinstance(exc, requests.exceptions.SSLError):
            try:
                import ssl

                details["ssl_version"] = ssl.OPENSSL_VERSION
            except Exception:
                pass

        elif isinstance(exc, requests.exceptions.ConnectionError):
            details["dns_ok"] = self._check_dns_resolution(url)
            from urllib.parse import urlparse

            parsed = urlparse(url)
            details["target_host"] = parsed.hostname
            details["target_port"] = parsed.port or (443 if parsed.scheme == "https" else 80)

        return details

    def _wrap_exception(self, exc: requests.exceptions.RequestException, url: str, debug: Dict[str, Any]):
        """Return a domain exception carrying the debug dict."""
        summary = f" [{', '.join(f'{k}={v}' for k, v in debug.items() if k in {'duration','status_code','dns_ok','timeout','error_type'})}]"
        msg = f"{type(exc).__name__} for {url}{summary}"

        if isinstance(exc, requests.exceptions.HTTPError):
            return ConfluenceClientException.from_http_error(exc.response, message=msg, debug_details=debug)
        if isinstance(exc, requests.exceptions.Timeout):
            return ConfluenceTimeoutError(msg, debug_details=debug, resource=url)
        if isinstance(exc, requests.exceptions.SSLError):
            return ConfluenceClientException(msg, debug_details=debug, resource=url, is_retryable=False)
        if isinstance(exc, requests.exceptions.ConnectionError):
            return ConfluenceClientException(msg, debug_details=debug, resource=url, is_retryable=True)
        # Fallback
        return ConfluenceClientException(msg, debug_details=debug, resource=url, is_retryable=False)

    # -------------------------------
    # Attachments
    # -------------------------------
    def _is_valid_attachment(self, attachment: dict) -> bool:
        from ..config.helpers import is_temp_file
        title = attachment.get("title", "")
        if self.config and is_temp_file(self.config, title):
            return False
        if title.startswith((".", "~")):
            return False
        return True

    def _filter_current_attachments(self, all_attachments: List[dict]) -> List[dict]:
        return [
            att for att in all_attachments
            if att.get("status") == "current" and self._is_valid_attachment(att)
        ]

    def get_page_attachments_all_current(self, page_id: str, start: int = 0, limit: int = 50) -> List[dict]:
        retryer = create_search_retryer(self.config)
        try:
            def _get_attachments_call():
                return self.confluence.get_attachments_from_content(
                    page_id=page_id,
                    start=start,
                    limit=limit
                )

            response = retryer(_get_attachments_call)
            return self._filter_current_attachments(response.get("results", []))
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                raise ConfluenceClientException(
                    f"Failed to retrieve attachments for page {page_id}",
                    original_exception=e,
                    resource=page_id
                ) from e
            raise

    def get_page_referenced_attachments(self, page_id: str) -> List[dict]:
        try:
            logger.debug("Fetching page content & attachments in one call for page %s", page_id)
            page_data = self.get_page_content(
                page_id,
                expand="body.storage,children.attachment"
            ) or {}
            content_html = page_data.get('body', {}).get('storage', {}).get('value', '')
            attachment_results = page_data.get('children', {}).get('attachment', {}).get('results', [])

            current_attachments = self._filter_current_attachments(attachment_results)

            logger.debug("Page %s: Found %d total attachments", page_id, len(current_attachments))
            for att in current_attachments:
                logger.debug("  - Attachment: %s", att.get('title', 'Unknown'))

            if not current_attachments:
                logger.debug("No current attachments for page %s.", page_id)
                return []

            max_size = self.config.file_processing.max_content_size_for_filtering
            content_size = len(content_html.encode('utf-8'))
            logger.debug("Page %s: Content size %d bytes (max: %d)", page_id, content_size, max_size)

            if content_size > max_size:
                logger.warning(
                    "Page %s too large (%d bytes); skipping attachment filtering for safety.",
                    page_id, content_size
                )
                return []

            return self._extract_attachments_from_xml(content_html, current_attachments)

        except ConfluenceClientException:
            raise
        except Exception as e:
            raise ConfluenceClientException(
                f"Failed to process attachments for page {page_id}",
                original_exception=e,
                resource=page_id
            ) from e

    # -------------------------------
    # XML Parsing Helpers
    # -------------------------------
    def _extract_attachments_from_xml(self, content: str, attachments: List[dict]) -> List[dict]:
        """
        Parse Confluence storage XML to find explicitly referenced attachments.
        Uses fixed prefixes for speed & reliability.
        """
        # Fixed namespace map identical to original constants
        NS = {
            'ri': 'http://www.atlassian.com/schema/confluence/4/ri/',
            'ac': 'http://www.atlassian.com/schema/confluence/4/ac/'
        }

        try:
            stripped = content.strip()
            if not stripped.startswith(('<?xml', '<html')):
                stripped = f"""<root xmlns:ac="{NS['ac']}" xmlns:ri="{NS['ri']}">{stripped}</root>"""
            root = ET.fromstring(stripped)

            referenced: Set[str] = set()

            # 1️⃣ Draw.io macros
            for macro in root.findall(".//ac:structured-macro[@ac:name='drawio']", NS):
                for attr in ('diagramName', 'filename'):
                    param = macro.find(f".//ac:parameter[@ac:name='{attr}']", NS)
                    if param is not None and param.text:
                        referenced.add(param.text.strip())

            # 2️⃣ Standard ri:attachment tags
            for ri_att in root.findall(".//ri:attachment", NS):
                filename = ri_att.get(f"{{{NS['ri']}}}filename")
                if filename:
                    referenced.add(filename.strip())

            # 3️⃣ Links to attachments (href patterns)
            for link in root.findall(".//a[@href]"):
                href = link.get("href", "")
                if "/download/attachments/" in href or "/attachments/" in href:
                    # Extract filename from URL path
                    import urllib.parse
                    parsed_url = urllib.parse.urlparse(href)
                    path_parts = parsed_url.path.split('/')
                    if path_parts:
                        filename = path_parts[-1]
                        # Remove URL parameters like ?version=1&api=v2
                        filename = filename.split('?')[0]
                        if filename:
                            referenced.add(filename.strip())

            # 4️⃣ Catch-all filename in any attribute name
            for elem in root.iter():
                for attr_name, attr_value in elem.attrib.items():
                    if 'filename' in str(attr_name).lower():
                        referenced.add(attr_value.strip())

            # 5️⃣ Text content that might reference filenames
            for elem in root.iter():
                if elem.text:
                    text = elem.text.strip()
                    # Look for common file extensions in text
                    import re
                    file_pattern = r'\b[\w\-\.]+\.(?:pptx?|docx?|xlsx?|pdf|png|jpe?g|gif|zip|rar)\b'
                    matches = re.findall(file_pattern, text, re.IGNORECASE)
                    for match in matches:
                        referenced.add(match.strip())

            if not referenced:
                logger.debug("No attachment references found in page content")
                return []

            lower_refs = {f.lower() for f in referenced}
            matched_attachments = [att for att in attachments
                                 if att.get('title', '').lower() in lower_refs]

            logger.debug("Found %d referenced attachments out of %d total attachments",
                        len(matched_attachments), len(attachments))
            return matched_attachments

        except ET.ParseError as e:
            logger.warning("XML parsing failed for attachment filtering: %s", e)
            return []
        except Exception as e:
            logger.exception("Unexpected XML error: %s", e)
            return []

    # -------------------------------
    # Misc Public Methods
    # -------------------------------
    def _fetch_attachment_by_id_from_api(self, attachment_id: str) -> Dict:
        return _cached_fetch_attachment(self.url, attachment_id, self.verify_ssl)

    def get_attachment_by_id(self, attachment_id: str) -> Dict:
        return self._fetch_attachment_by_id_from_api(attachment_id)

    def get_attachment_content(self, attachment_id: str) -> bytes:
        retryer = create_api_retryer(self.config)
        try:
            meta = self.get_attachment_by_id(attachment_id)
            download_link = meta.get('_links', {}).get('download')
            if not download_link:
                raise ConfluenceClientException(
                    f"No download link for attachment {attachment_id}",
                    resource=attachment_id
                )
            download_url = self.url + download_link if download_link.startswith('/') else download_link
            return retryer(self._download_content_from_url, url=download_url)
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                raise ConfluenceClientException(
                    f"Unexpected error retrieving attachment {attachment_id}",
                    original_exception=e,
                    resource=attachment_id
                ) from e
            raise

    def _download_content_from_url(self, url: str) -> bytes:
        with self._safe_request_context('GET', url) as resp:
            return resp.content

    def get_page_content(self, page_id: str,
                         expand: str = "body.storage,version,ancestors,children.attachment") -> Dict:
        retryer = create_api_retryer(self.config)
        try:
            def _get_page_call():
                return self.confluence.get_page_by_id(page_id=page_id, expand=expand)

            page = retryer(_get_page_call)
            if page is None:
                raise ConfluenceNotFoundError(
                    f"Confluence page with ID '{page_id}' was not found.",
                    resource=page_id
                )
            return page
        except Exception as e:
            if not isinstance(e, (ConfluenceClientException, ConfluenceNotFoundError)):
                raise ConfluenceClientException(
                    f"Failed to retrieve page {page_id}",
                    original_exception=e,
                    resource=page_id
                ) from e
            raise

    def search_content(self, cql: str, limit: int = 100, start: int = 0,
                       expand: str = "version,ancestors,children.attachment") -> List[dict]:
        retryer = create_search_retryer(self.config)
        try:
            def _cql_call():
                return self.confluence.cql(cql=cql, start=start, limit=limit, expand=expand)

            response = retryer(_cql_call)
            return response.get("results", [])
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                raise ConfluenceClientException(
                    f"CQL search failed: '{cql}'",
                    original_exception=e
                ) from e
            raise

    def export_page_as_pdf(self, page_id: str) -> bytes:
        retryer = create_api_retryer(self.config)
        try:
            logger.info("Exporting page %s as PDF", page_id)

            def _pdf_export_call():
                return self.confluence.get_page_as_pdf(page_id=page_id)

            pdf_content = retryer(_pdf_export_call)
            if pdf_content is None:
                raise ConfluenceClientException(
                    f"PDF export returned None for page {page_id}",
                    resource=page_id,
                    operation="export_page_as_pdf"
                )
            logger.info("PDF export successful for page %s", page_id)
            return pdf_content
        except Exception as e:
            if not isinstance(e, ConfluenceClientException):
                logger.error("Failed to export page %s as PDF: %s", page_id, e, exc_info=True)
                raise ConfluenceClientException(
                    f"Failed to export page {page_id} as PDF: {e}",
                    original_exception=e,
                    resource=page_id,
                    operation="export_page_as_pdf"
                ) from e
            raise
