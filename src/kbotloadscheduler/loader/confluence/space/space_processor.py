"""
Orchestrates the discovery of content in a single Confluence space,
creating DocumentBeans that serve as "work orders" for the ContentDownloader.
"""
import logging
import os
import re
import json
from functools import lru_cache
from typing import Dict, List, Optional, Any, Tuple

from kbotloadscheduler.bean.beans import DocumentBean, SourceBean
from kbotloadscheduler.exceptions.confluence_exceptions import (
    ConfluenceClientException,
    ConfluenceNotFoundError,
    ConfluenceTimeoutError,
)
from kbotloadscheduler.exceptions.base_exceptions import LoaderException
from ..config.schema import ConfluenceConfig
from ..processors import bean_factory
from ..processors.drawio_detector import DrawioDetector
from ..search.cql_search import CqlSearch

logger = logging.getLogger(__name__)


class SpaceProcessor:
    def _log_progress(self, action: str, index: int, total: int, page_title: str) -> None:
        """
        Log the progress of processing pages at regular intervals.

        Args:
            action (str): The action being performed (e.g., 'Processing', 'Streaming').
            index (int): The current page index (1-based).
            total (int): The total number of pages.
            page_title (str): The title of the current page.
        """
        if index % 50 == 0 or index == 1:
            logger.info("🔄 %s page %d/%d: '%s' (%.1f%%)",
                        action, index, total, page_title, index / total * 100)
    """
    Discovers and creates DocumentBeans for all content in a Confluence space.
    """

    DRAWIO_PNG_EXPORT_REGEX = re.compile(r'-\d{10,13}\.png\Z', re.IGNORECASE)

    MAX_CHILD_PAGE_DEPTH = 50
    MIN_FILENAME_LENGTH = 20
    VALID_DUPLICATE_FILENAME_STRATEGIES = ["append_id", "append_counter", "overwrite"]
    DRAWIO_INDICATORS = ["diagram", "drawio", "draw.io"]

    def __init__(self, client, cql_search: CqlSearch, base_url: str):
        self.client = client
        self.cql_search = cql_search
        self.base_url = base_url
        self.drawio_detector = DrawioDetector()

    # ------------------------------------------------------------------
    # Public entry points
    # ------------------------------------------------------------------

    def process(self, space_key: str, source: SourceBean, config: ConfluenceConfig) -> List[DocumentBean]:
        """
        Discover and create DocumentBeans for all content in a Confluence space (eager version).

        Args:
            space_key (str): The key of the Confluence space to process.
            source (SourceBean): The source bean representing the origin of the documents.
            config (ConfluenceConfig): The configuration for processing.

        Returns:
            List[DocumentBean]: A list of DocumentBeans representing all discovered content.

        Raises:
            ValueError: If the configuration is invalid.
        """
        logger.info("Start processing space: %s", space_key)
        self._validate_config(config)

        logger.info("🚀 Using optimized bulk processing for space '%s'", space_key)
        all_pages = self.cql_search.search_all_pages_in_space(space_key, config)
        logger.info("📊 Space '%s': Found %d total pages in bulk query", space_key, len(all_pages))

        all_documents: List[DocumentBean] = []
        for i, page_data in enumerate(all_pages, 1):
            page_title = page_data.get("title", "Unknown")
            self._log_progress("Processing", i, len(all_pages), page_title)
            all_documents.extend(self._process_single_page_streaming(page_data, source, config))

        logger.info("Space %s processed. %d documents found in total.", space_key, len(all_documents))
        return all_documents


    def process_streaming(self, space_key: str, source: SourceBean, config: ConfluenceConfig):
        """
        Discover and create DocumentBeans for all content in a Confluence space (lazy/streaming version).

        Args:
            space_key (str): The key of the Confluence space to process.
            source (SourceBean): The source bean representing the origin of the documents.
            config (ConfluenceConfig): The configuration for processing.

        Yields:
            DocumentBean: Each discovered DocumentBean one at a time.

        Raises:
            ValueError: If the configuration is invalid.
        """
        logger.info("Start streaming processing of space: %s", space_key)
        self._validate_config(config)

        logger.info("🚀 Using optimized bulk streaming for space '%s'", space_key)
        all_pages = self.cql_search.search_all_pages_in_space(space_key, config)
        logger.info("📊 Space '%s': Found %d total pages for streaming", space_key, len(all_pages))

        documents_count = 0
        for i, page_data in enumerate(all_pages, 1):
            page_title = page_data.get("title", "Unknown")
            self._log_progress("Streaming", i, len(all_pages), page_title)
            for document in self._process_single_page_streaming(page_data, source, config):
                documents_count += 1
                yield document

        logger.info("Space %s processed in streaming mode. %d documents yielded in total.",
                    space_key, documents_count)

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------

    def _validate_config(self, config: ConfluenceConfig) -> None:
        """
        Validate the provided configuration for processing.

        Args:
            config (ConfluenceConfig): The configuration to validate.

        Raises:
            ValueError: If any configuration parameter is invalid.
        """
        if not isinstance(config.basic.child_page_depth, int) or config.basic.child_page_depth < 0:
            raise ValueError("child_page_depth must be >= 0")
        if config.basic.child_page_depth > self.MAX_CHILD_PAGE_DEPTH:
            raise ValueError(f"child_page_depth must be <= {self.MAX_CHILD_PAGE_DEPTH}")
        if config.attachments.file_extensions:
            for ext in config.attachments.file_extensions:
                if not isinstance(ext, str) or not ext.strip():
                    raise ValueError(f"Invalid file extension: {ext}. Extensions must be non-empty strings.")
        if hasattr(config.file_processing, "max_filename_length") and config.file_processing.max_filename_length < self.MIN_FILENAME_LENGTH:
            raise ValueError(f"max_filename_length must be at least {self.MIN_FILENAME_LENGTH} characters")
        if (hasattr(config.file_processing, "duplicate_filename_strategy") and
            config.file_processing.duplicate_filename_strategy not in self.VALID_DUPLICATE_FILENAME_STRATEGIES):
            raise ValueError(f"duplicate_filename_strategy must be one of {self.VALID_DUPLICATE_FILENAME_STRATEGIES}")

    # ------------------------------------------------------------------
    # Per-page processing
    # ------------------------------------------------------------------

    def _process_single_page_streaming(self, page_data: Dict[str, Any], source: SourceBean,
                                       config: ConfluenceConfig):
        """
        Yield all DocumentBeans that belong to a single page, including the page itself,
        its Draw.io diagrams, and its attachments.

        Args:
            page_data (Dict[str, Any]): The data for the page.
            source (SourceBean): The source bean.
            config (ConfluenceConfig): The configuration for processing.

        Yields:
            DocumentBean: Each DocumentBean related to the page.
        """
        page_id = page_data.get("id")
        if not page_id:
            logger.warning("Unable to process a page without ID. Data: %s", page_data)
            return

        page_bean = self._create_page_bean(page_data, source)
        if not page_bean:
            logger.warning("Skipping page %s because its DocumentBean could not be created.", page_id)
            return

        yield page_bean

        # Draw.io diagrams
        yield from self._process_drawio_diagrams_streaming(page_data, source, config)

        # Attachments
        yield from self._process_page_attachments_streaming(page_data, source, config)

    # ------------------------------------------------------------------
    # Bean creators
    # ------------------------------------------------------------------

    @staticmethod
    @lru_cache(maxsize=64)
    def _get_normalized_extensions(allowed_extensions: tuple) -> list:
        """
        Normalize a tuple or list of file extensions to lowercase and remove leading dots.

        Args:
            allowed_extensions (tuple): The allowed file extensions.

        Returns:
            list: The normalized list of extensions.

        Raises:
            ValueError: If allowed_extensions is not a non-empty tuple or list.
        """
        if not allowed_extensions or not isinstance(allowed_extensions, (tuple, list)):
            raise ValueError("allowed_extensions must be a non-empty tuple or list")
        return [e.lower().lstrip(".") for e in allowed_extensions]

    def _create_page_bean(self, page_data: Dict[str, Any], source: SourceBean) -> Optional[DocumentBean]:
        """
        Create a DocumentBean for a page.

        Args:
            page_data (Dict[str, Any]): The data for the page.
            source (SourceBean): The source bean.

        Returns:
            Optional[DocumentBean]: The created DocumentBean, or None if creation failed.

        Raises:
            LoaderException: If a loader-specific error occurs.
        """
        page_id = page_data.get("id")
        try:
            return bean_factory.create_page_bean(page_data, source, self.base_url)
        except LoaderException as e:
            logger.error("Loader exception creating bean for page %s: %s", page_id, e)
            raise
        except (ValueError, KeyError, TypeError, json.JSONDecodeError) as e:
            logger.error("Data validation/JSON error creating bean for page %s: %s", page_id, e)
            return None

    def _create_attachment_bean(
        self,
        att_data: Dict[str, Any],
        source: SourceBean,
        page_id: str,
        config: ConfluenceConfig,
    ) -> Optional[DocumentBean]:
        """
        Create a DocumentBean for an attachment.

        Args:
            att_data (Dict[str, Any]): The data for the attachment.
            source (SourceBean): The source bean.
            page_id (str): The ID of the page the attachment belongs to.
            config (ConfluenceConfig): The configuration for processing.

        Returns:
            Optional[DocumentBean]: The created DocumentBean, or None if creation failed.

        Raises:
            LoaderException: If a loader-specific error occurs.
        """
        attachment_title = att_data.get("title", "")
        ...
        try:
            return bean_factory.create_attachment_bean(att_data, source, page_id, self.base_url)
        except LoaderException as e:
            logger.error("Loader exception for attachment '%s' (page %s): %s", attachment_title, page_id, e)
            raise
        except (ValueError, KeyError, TypeError, json.JSONDecodeError) as e:
            logger.error("Data/JSON error for attachment '%s' (page %s): %s", attachment_title, page_id, e)
            return None

    # ------------------------------------------------------------------
    # Attachment & Draw.io filtering
    # ------------------------------------------------------------------

    def _should_include_attachment(self, filename: str, allowed_extensions: List[str]) -> bool:
        """
        Determine if an attachment should be included based on its filename and allowed extensions.

        Args:
            filename (str): The name of the attachment file.
            allowed_extensions (List[str]): The list of allowed file extensions.

        Returns:
            bool: True if the attachment should be included, False otherwise.
        """
        if not allowed_extensions:
            logger.debug("Attachment '%s' skipped because 'file_extensions' config is empty.", filename)
            return False
        if not filename:
            return False

        _, ext = os.path.splitext(filename.lower())
        file_ext = ext[1:]  # Remove the leading '.'
        normalized_allowed_extensions = self._get_normalized_extensions(tuple(allowed_extensions))
        return file_ext in normalized_allowed_extensions

    def _should_exclude_drawio_png_export(self, filename: str, config: ConfluenceConfig) -> bool:
        """
        Decide whether a PNG attachment should be ignored because it is a Draw.io auto-export.

        Args:
            filename (str): The name of the attachment file.
            config (ConfluenceConfig): The configuration for processing.

        Returns:
            bool: True if the PNG should be excluded, False otherwise.
        """
        # 1. Feature is disabled OR we want to keep PNG exports
        if not config.attachments.extract_drawio_as_documents:
            return False
        if config.attachments.include_drawio_png_exports:
            return False

        # 2. Must be a PNG file
        if not filename or not filename.lower().endswith('.png'):
            return False

        # 3. Does the name scream "Draw.io export"?
        return self._looks_like_drawio_png_export(filename)

    def _looks_like_drawio_png_export(self, filename: str) -> bool:
        """
        Return True if filename contains Draw.io keywords or matches the auto-export suffix pattern.

        Args:
            filename (str): The name of the file.

        Returns:
            bool: True if the filename looks like a Draw.io PNG export, False otherwise.
        """
        name_lower = filename.lower()
        keyword_hit = any(ind in name_lower for ind in self.DRAWIO_INDICATORS)
        suffix_hit = self.DRAWIO_PNG_EXPORT_REGEX.search(filename) is not None
        return keyword_hit or suffix_hit

    # ------------------------------------------------------------------
    # Draw.io processing
    # ------------------------------------------------------------------

    def _process_drawio_diagrams_streaming(self, page_data, source, config):
        """
        Yield DocumentBeans for Draw.io diagrams detected in a page.
        """
        if not config.attachments.extract_drawio_as_documents:
            return

        page_id = page_data.get("id")
        if not page_id:
            return

        try:
            page_content_body, attachments = self._fetch_data_for_diagram_detection(page_id)
            if not page_content_body:
                return

            diagrams = self.drawio_detector.detect_drawio_diagrams(page_content_body, page_id, attachments)
            if diagrams:
                logger.debug("Page %s: Detected %d Draw.io diagrams.", page_id, len(diagrams))
                yield from self._create_beans_for_valid_diagrams(diagrams, page_data, source, config)

        except (ConfluenceNotFoundError, ConfluenceTimeoutError, ConfluenceClientException) as e:
            logger.warning("API error processing Draw.io for page %s: %s", page_id, e)
        except Exception as e:
            logger.error("Unexpected error processing Draw.io for page %s: %s (%s)",
                         page_id, type(e).__name__, e, exc_info=True)

    def _create_beans_for_valid_diagrams(self, diagrams, page_data, source, config):
        """
        Yield DocumentBeans for valid Draw.io diagrams after filtering.

        Args:
            diagrams (list): List of detected diagrams.
            page_data (dict): The data for the page.
            source (SourceBean): The source bean.
            config (ConfluenceConfig): The configuration for processing.

        Yields:
            DocumentBean: Each valid DocumentBean for a Draw.io diagram.
        """
        for diagram in diagrams:
            if self._should_include_drawio_diagram(diagram, config):
                bean = self._create_drawio_document_safe(diagram, page_data, source)
                if bean:
                    logger.debug("Draw.io document created: %s - %s", bean.id, bean.name)
                    yield bean
            else:
                logger.debug("Draw.io diagram ignored by filter: %s", diagram.title or diagram.diagram_id)

    def _should_include_drawio_diagram(self, diagram, config: ConfluenceConfig) -> bool:
        """
        Determine if a Draw.io diagram should be included as a document.

        Args:
            diagram: The diagram object.
            config (ConfluenceConfig): The configuration for processing.

        Returns:
            bool: True if the diagram should be included, False otherwise.
        """
        if not config.attachments.file_extensions:
            logger.debug("Draw.io diagram '%s' skipped because 'file_extensions' is empty.",
                         diagram.title or diagram.diagram_id)
            return False

        normalized_extensions = self._get_normalized_extensions(tuple(config.attachments.file_extensions))
        if diagram.diagram_type in ["inline", "macro", "embedded"]:
            return "drawio" in normalized_extensions

        if diagram.title:
            return self._should_include_attachment(diagram.title, config.attachments.file_extensions)

        return "drawio" in normalized_extensions  # Fallback for untitled attachment diagrams

    def _create_drawio_document_safe(self, diagram, page_data: Dict[str, Any], source: SourceBean) -> Optional[DocumentBean]:
        """
        Safely create a DocumentBean for a Draw.io diagram, handling exceptions.

        Args:
            diagram: The diagram object.
            page_data (Dict[str, Any]): The data for the page.
            source (SourceBean): The source bean.

        Returns:
            Optional[DocumentBean]: The created DocumentBean, or None if creation failed.

        Raises:
            LoaderException: If a loader-specific error occurs.
        """
        try:
            return self._create_drawio_document(diagram, page_data, source)
        except LoaderException as e:
            logger.error("Loader exception creating document for diagram %s: %s", diagram.diagram_id, e)
            raise
        except (ValueError, KeyError, TypeError) as e:
            logger.error("Data validation error creating document for diagram %s: %s", diagram.diagram_id, e)
            return None
        except Exception as e:
            logger.error("Unexpected error creating document for diagram %s: %s (%s)",
                         diagram.diagram_id, type(e).__name__, e, exc_info=True)
            return None

    def _create_drawio_document(self, diagram, page_data: Dict[str, Any], source: SourceBean) -> Optional[DocumentBean]:
        """
        Create a DocumentBean for a Draw.io diagram.

        Args:
            diagram: The diagram object.
            page_data (Dict[str, Any]): The data for the page.
            source (SourceBean): The source bean.

        Returns:
            Optional[DocumentBean]: The created DocumentBean, or None if creation failed.
        """
        diagram_data = {
            "diagram_id": diagram.diagram_id,
            "title": diagram.title,
            "diagram_type": diagram.diagram_type,
            "attachment_id": diagram.attachment_id,
        }
        return bean_factory.create_drawio_bean(diagram_data, source, page_data, self.base_url)

    # ------------------------------------------------------------------
    # Attachment helpers
    # ------------------------------------------------------------------

    def _process_page_attachments_streaming(self, page_data: Dict[str, Any], source: SourceBean,
                                            config: ConfluenceConfig):
        """
        Yield DocumentBeans for all attachments referenced in a page's content using
        an optimized, single API call per page.
        """
        if not config.attachments.include_attachments:
            logger.debug("Page attachments disabled; skipping for page %s", page_data.get("id", "unknown"))
            return

        page_id = page_data.get("id")
        if not page_id:
            return

        logger.debug("Page %s: Processing attachments (mode='content_used', extensions=%s)",
                     page_id, config.attachments.file_extensions)

        try:
            # MODIFICATION : Appel direct à la méthode optimisée. Plus de boucle de pagination ici.
            attachments = self.client.get_page_referenced_attachments(page_id=page_id)

            logger.debug("Page %s: Found %d attachments referenced in content via optimized call.", page_id, len(attachments))

            processed_count = 0
            for att_data in attachments:
                attachment_filename = att_data.get("title", "")

                # Apply file extension filtering first
                if not self._should_include_attachment(attachment_filename, config.attachments.file_extensions):
                    logger.debug("Skipping attachment '%s' due to file extension filtering (allowed: %s)",
                               attachment_filename, config.attachments.file_extensions)
                    continue

                # Then check for Draw.io PNG exports if necessary
                if self._should_exclude_drawio_png_export(attachment_filename, config):
                    logger.debug("Skipping Draw.io auto-exported PNG: %s", attachment_filename)
                    continue

                attachment_bean = self._create_attachment_bean(att_data, source, page_id, config)
                if attachment_bean:
                    processed_count += 1
                    logger.debug("Attachment processed: %s - %s", attachment_bean.id, attachment_bean.name)
                    yield attachment_bean

            if processed_count > 0:
                logger.debug("Page %s: %d attachments processed from this page.", page_id, processed_count)

        except (ConfluenceNotFoundError, ConfluenceTimeoutError, ConfluenceClientException) as e:
            logger.warning("API error during attachment processing for page %s: %s", page_id, e)
        except Exception as e:
            logger.error("Unexpected error during attachment processing for page %s: %s (%s)",
                         page_id, type(e).__name__, e, exc_info=True)

    # ------------------------------------------------------------------
    # Generic utilities
    # ------------------------------------------------------------------

    def _fetch_data_for_diagram_detection(self, page_id: str) -> Tuple[Optional[str], List[Dict[str, Any]]]:
        """
        Fetch page content and attachments in a single API call for Draw.io detection.
        """
        try:
            # Un seul appel API pour le contenu et les pièces jointes
            page_data = self.client.get_page_content(page_id, expand="body.storage,children.attachment")
            if not page_data:
                return None, []

            content_body = page_data.get('body', {}).get('storage', {}).get('value', '')
            attachments = page_data.get('children', {}).get('attachment', {}).get('results', [])

            # On ne garde que les pièces jointes valides
            current_attachments = self.client._filter_current_attachments(attachments)

            return content_body, current_attachments

        except (ConfluenceNotFoundError, ConfluenceTimeoutError, ConfluenceClientException) as e:
            logger.warning("API error retrieving content/attachments for page %s for diagram detection: %s", page_id, e)
            return None, []
