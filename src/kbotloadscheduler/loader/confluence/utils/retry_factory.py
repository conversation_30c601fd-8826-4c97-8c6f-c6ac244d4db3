"""
Factory for creating standardized Tenacity Retrying objects.
This centralizes retry logic for API calls, searches, etc.
"""
import logging

from tenacity import (
    Retrying,
    wait_exponential,
    stop_after_attempt,
    retry_if_exception,
    retry_if_result,
    RetryCallState,
)

from ....exceptions.utils import is_retryable_exception
from ..config.schema import ConfluenceConfig

logger = logging.getLogger(__name__)


def _is_result_empty(value) -> bool:
    """
    Return True if the result is considered empty and should trigger a retry.

    This function intentionally only checks for `None`. An empty list (`[]`) from a search
    is a valid result and should NOT trigger a retry. This retry condition is meant
    for API calls that are expected to return a single resource object.
    """
    return value is None


def _log_before_sleep(retry_state: RetryCallState):
    """Log the retry attempt with details on why it's happening."""
    log_level = logging.INFO
    if retry_state.outcome.failed:
        ex = retry_state.outcome.exception()
        logger.log(
            log_level,
            f"Attempt {retry_state.attempt_number} failed for function "
            f"'{retry_state.fn.__name__}': {ex}. "
            f"Retrying in {retry_state.next_action.sleep:.2f} seconds...",
        )
    else:
        logger.log(
            log_level,
            f"Attempt {retry_state.attempt_number} for function '{retry_state.fn.__name__}' "
            f"returned an empty result. Retrying in {retry_state.next_action.sleep:.2f} seconds...",
        )


def _create_retryer(
    config: ConfluenceConfig,
    *,
    retry_empty: bool = False,
) -> Retrying:
    """
    Internal factory that builds a Tenacity Retrying instance.

    Args:
        config: Configuration object containing retry policy.
        retry_empty: If True, also retry when the wrapped function returns None.
                     If False, only retry on retryable exceptions.

    Returns:
        A configured Retrying instance.
    """
    retry_conditions = retry_if_exception(is_retryable_exception)
    if retry_empty:
        retry_conditions |= retry_if_result(_is_result_empty)

    return Retrying(
        wait=wait_exponential(
            multiplier=config.performance.retry_delay_seconds,
            min=1,
            max=60,
        ),
        stop=stop_after_attempt(config.performance.retry_attempts),
        retry=retry_conditions,
        before_sleep=_log_before_sleep,
        reraise=True,
    )


def create_api_retryer(config: ConfluenceConfig) -> Retrying:
    """
    Creates a standard tenacity Retrying instance for API calls that fetch a single resource.
    This retryer will trigger on retryable exceptions OR if the function returns None.
    """
    return _create_retryer(config, retry_empty=True)


def create_search_retryer(config: ConfluenceConfig) -> Retrying:
    """
    Creates a tenacity Retrying instance for search-like API calls.
    This retryer will ONLY trigger on retryable exceptions, not on an empty list result,
    as an empty list is a valid search outcome.
    """
    return _create_retryer(config, retry_empty=False)
