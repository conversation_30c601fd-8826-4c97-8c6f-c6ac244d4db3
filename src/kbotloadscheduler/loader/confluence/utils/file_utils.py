"""
Utilities for file handling, filename sanitization, and temporary file management.
"""
import logging
import os
import re
import tempfile
from typing import Callable

# Suppose que gcs_utils est importable pour la vérification d'existence
# Adaptez l'import si nécessaire
from kbotloadscheduler.gcs import gcs_utils


def clean_filename(basename: str) -> str:
    """Normalize a string to be a safe filename base. All separators become underscores."""
    cleaned = basename.lower()
    # Remplacer les espaces ET les tirets par des underscores
    cleaned = re.sub(r'[\s\-]+', '_', cleaned)
    # Supprimer tous les caractères qui ne sont pas des lettres, chiffres ou underscores
    cleaned = re.sub(r'[^\w]', '', cleaned) # \w inclut déjà l'underscore
    # Réduire les underscores multiples
    cleaned = re.sub(r'_+', '_', cleaned)
    # Supprimer les underscores au début ou à la fin
    cleaned = cleaned.strip('_')

    return cleaned if cleaned else "sans_titre"


def create_safe_filename(original_filename: str, item_id: str) -> str:
    """
    Creates a safe and clean filename by calling the centralized clean_filename function.
    """
    base_name, extension = os.path.splitext(original_filename)

    # Appel de la nouvelle fonction clean_filename sur le nom de base
    cleaned_base = clean_filename(base_name)

    # On s'assure que l'extension est en minuscule et a un point
    safe_extension = extension.lower()
    if safe_extension and not safe_extension.startswith('.'):
        safe_extension = '.' + safe_extension

    return f"{cleaned_base}{safe_extension}"


def create_unique_filename(filename: str, item_id: str, output_path: str, strategy: str) -> str:
    """
    Create a unique filename to avoid conflicts in GCS.

    Args:
        filename: Base (safe) filename, e.g., "rapport.pdf".
        item_id: Unique Confluence ID for the item.
        output_path: The GCS destination directory.
        strategy: Conflict resolution strategy ("error", "overwrite", "append_number", "append_id").

    Returns:
        A unique filename.
    """
    # La stratégie "overwrite" ne nécessite aucune action, le fichier sera écrasé.
    if strategy == "overwrite":
        return filename

    destination_path = os.path.join(output_path, filename)

    # On vérifie VRAIMENT l'existence du fichier sur GCS
    if not gcs_utils.exists_file_gcs(destination_path):
        return filename  # Pas de conflit

    # Conflit détecté, on applique la stratégie
    logging.warning(f"Filename conflict detected for '{filename}' in '{output_path}'. Applying strategy: '{strategy}'.")

    if strategy == "error":
        raise FileExistsError(f"File '{filename}' already exists in '{output_path}' and strategy is 'error'.")

    base, ext = os.path.splitext(filename)

    if strategy == "append_id":
        clean_id = re.sub(r'\W+', '_', item_id)
        unique_filename = f"{base}_{clean_id}{ext}"
        # On vérifie quand même, par sécurité extrême
        if not gcs_utils.exists_file_gcs(os.path.join(output_path, unique_filename)):
            return unique_filename
        # Si, par malchance, il existe, on passe à la stratégie numérique comme fallback
        logging.warning(f"Fallback to append_number for '{unique_filename}' as it already exists.")

    if strategy == "append_number" or strategy == "append_id": # Gère le fallback de append_id
        i = 1
        while True:
            new_filename = f"{base}_{i}{ext}"
            if not gcs_utils.exists_file_gcs(os.path.join(output_path, new_filename)):
                return new_filename
            i += 1

    # Stratégie par défaut ou inconnue, on retourne le nom original (équivaut à "overwrite")
    logging.warning(f"Unknown or unhandled filename conflict strategy: '{strategy}'. Defaulting to overwrite.")
    return filename


def download_with_temp_file(download_func: Callable, *args, **kwargs) -> str:
    """
    Helper method for temporary file management using a context manager.
    (Cette fonction est inchangée et correcte)
    """
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_path = temp_file.name
    temp_file.close()

    try:
        download_func(temp_path, *args, **kwargs)
        return temp_path
    except Exception:
        if os.path.exists(temp_path):
            os.remove(temp_path)
        raise
