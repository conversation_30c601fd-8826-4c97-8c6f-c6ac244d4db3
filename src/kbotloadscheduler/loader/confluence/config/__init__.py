"""Configuration management for Confluence loader.

This module provides a Pydantic-based configuration schema and a builder
for creating validated Confluence configuration objects.

Example usage:
from kbotloadscheduler.confluence.config import build_config_from_source
cfg = build_config_from_source(source_bean)
"""


from .schema import (
    ConfluenceConfig,
    AuthConfig,
    AttachmentConfig,
    FilteringConfig,
    BasicConfig,
    PerformanceConfig,
    FileProcessingConfig,
)
from . import defaults
from .helpers import (
    should_include_attachment,
    should_include_by_labels,
    is_temp_file,
    contains_html_indicators,
    is_filename_too_long,
    should_include_by_date,
    create_minimal_config,
)

__all__ = [
    # Main configuration schema
    "ConfluenceConfig",
    # Nested configuration models
    "AuthConfig",
    "AttachmentConfig",
    "FilteringConfig",
    "BasicConfig",
    "PerformanceConfig",
    "FileProcessingConfig",
    # Helper functions
    "should_include_attachment",
    "should_include_by_labels",
    "is_temp_file",
    "contains_html_indicators",
    "is_filename_too_long",
    "should_include_by_date",
    "create_minimal_config",
    # Constants
    "DEFAULT_ATTACHMENT_LIMIT",
    "DEFAULT_ALLOWED_FILE_EXTENSIONS",
]

# Export constants from defaults for backward compatibility
DEFAULT_ATTACHMENT_LIMIT = getattr(defaults, "DEFAULT_ATTACHMENT_LIMIT", 50)
DEFAULT_ALLOWED_FILE_EXTENSIONS = getattr(defaults, "DEFAULT_ALLOWED_FILE_EXTENSIONS", ["pptx"])
