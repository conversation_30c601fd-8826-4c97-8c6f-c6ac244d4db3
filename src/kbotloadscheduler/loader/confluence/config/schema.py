"""Pydantic-based schema for Confluence configuration.

This module defines the configuration structure, types, defaults, and
validation rules in a declarative way using Pydantic V2.
"""

# --- The rest of your file as it was ---
from typing import Literal

from pydantic import BaseModel, Field, NonNegativeInt, PositiveInt, conint, model_validator

from . import defaults


class AuthConfig(BaseModel):
    """Authentication configuration for Confluence."""
    confluence_auth_mode: Literal[*defaults.VALID_AUTH_MODES] = "global"


class AttachmentConfig(BaseModel):
    """Attachment handling configuration."""
    include_attachments: bool = False
    attachment_filter_mode: Literal[*defaults.VALID_ATTACHMENT_FILTER_MODES] = "content_used"
    file_extensions: list[str] = Field(default_factory=lambda: list(defaults.DEFAULT_ALLOWED_FILE_EXTENSIONS))
    extract_drawio_as_documents: bool = False
    include_drawio_png_exports: bool = False


class FilteringConfig(BaseModel):
    """Content filtering configuration."""
    labels: list[str] | None = None
    exclude_labels: list[str] | None = None
    last_modified_days: PositiveInt | None = None
    custom_cql: str | None = None


class BasicConfig(BaseModel):
    """Basic Confluence configuration."""
    spaces: list[str] = Field(default_factory=list)
    max_results: PositiveInt | None = None
    export_format: Literal[*defaults.VALID_EXPORT_FORMATS] = "pdf"
    include_content_in_metadata: bool = False
    include_child_pages: bool = True
    child_page_depth: NonNegativeInt = 15


class PerformanceConfig(BaseModel):
    """Performance and reliability configuration."""
    parallel_downloads: bool = False
    max_parallel_workers: PositiveInt = 1
    enable_caching: bool = True
    cache_ttl_minutes: PositiveInt = 60
    retry_attempts: NonNegativeInt = 3
    retry_delay_seconds: NonNegativeInt = 2
    circuit_breaker_threshold: PositiveInt = 5
    circuit_breaker_timeout_seconds: PositiveInt = 60
    enable_metrics: bool = True
    use_memory_efficient_processing: bool = True


class FileProcessingConfig(BaseModel):
    """File processing configuration."""
    duplicate_filename_strategy: Literal[*defaults.VALID_DUPLICATE_FILENAME_STRATEGIES] = "append_id"
    temp_extensions: list[str] = Field(default_factory=lambda: defaults.DEFAULT_TEMP_EXTENSIONS)
    html_indicators: list[bytes] = Field(default_factory=lambda: defaults.DEFAULT_HTML_INDICATORS)
    max_filename_length: conint(ge=defaults.MIN_FILENAME_LENGTH) = defaults.DEFAULT_MAX_FILENAME_LENGTH
    default_timeout: PositiveInt = defaults.DEFAULT_TIMEOUT
    max_content_size_for_filtering: PositiveInt = defaults.DEFAULT_MAX_CONTENT_SIZE


class ConfluenceConfig(BaseModel):
    """Main configuration class for the Confluence loader."""
    auth: AuthConfig = Field(default_factory=AuthConfig)
    basic: BasicConfig = Field(default_factory=BasicConfig)
    attachments: AttachmentConfig = Field(default_factory=AttachmentConfig)
    filtering: FilteringConfig = Field(default_factory=FilteringConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    file_processing: FileProcessingConfig = Field(default_factory=FileProcessingConfig)

    @model_validator(mode='after')
    def check_spaces_is_not_empty(self) -> 'ConfluenceConfig':
        if not self.basic.spaces:
            raise ValueError(
                "At least one Confluence space must be specified via the 'spaces' "
                "or kbot 'space_key') configuration."
            )
        return self
