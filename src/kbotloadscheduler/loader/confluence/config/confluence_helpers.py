"""Confluence configuration helper functions.

This module contains business logic and helper functions that work with
Confluence configuration objects.
"""

import os
from datetime import datetime, timedelta, UTC
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .config.schema import ConfluenceConfig


def should_include_attachment(config: "ConfluenceConfig", attachment_name: str) -> bool:
    """
    Checks if an attachment should be included based on the list
    of file extensions provided during initialization.

    Args:
        config: The ConfluenceConfig instance
        attachment_name: Name of the attachment to check

    Returns:
        bool: True if attachment should be included, False otherwise
    """
    if not config.attachments.include_attachments:
        return False

    if not config.attachments.file_extensions:
        return False

    # There is no longer a hardcoded list here!
    allowed_extensions = config.attachments.file_extensions

    _, ext = os.path.splitext(attachment_name.lower())
    file_ext = ext[1:] if ext.startswith('.') else ext

    normalized_allowed = [e.lower().lstrip('.') for e in allowed_extensions]

    return file_ext in normalized_allowed


def should_include_by_labels(config: "ConfluenceConfig", page_labels: list[str]) -> bool:
    """Check if page should be included based on labels.

    Args:
        config: The ConfluenceConfig instance
        page_labels: List of labels for the page

    Returns:
        bool: True if page should be included, False otherwise
    """
    # Check exclude labels first
    if config.filtering.exclude_labels:
        for exclude_label in config.filtering.exclude_labels:
            if exclude_label in page_labels:
                return False  # Exclude this page

    # If we have required labels, check if at least one is present
    if config.filtering.labels:
        for required_label in config.filtering.labels:
            if required_label in page_labels:
                return True  # At least one required label found
        return False  # No required label found

    return True  # No label restrictions or labels OK


def is_temp_file(config: "ConfluenceConfig", filename: str) -> bool:
    """Check if a file is a temporary file based on configured extensions.

    Args:
        config: The ConfluenceConfig instance
        filename: Name of the file to check

    Returns:
        bool: True if file is temporary, False otherwise
    """
    filename_lower = filename.lower()
    return any(filename_lower.endswith(ext) for ext in config.file_processing.temp_extensions)


def contains_html_indicators(config: "ConfluenceConfig", content: bytes) -> bool:
    """Check if content contains HTML indicators.

    Args:
        config: The ConfluenceConfig instance
        content: Content to check (as bytes)

    Returns:
        bool: True if content contains HTML indicators, False otherwise
    """
    content_lower = content.lower()
    return any(indicator in content_lower for indicator in config.file_processing.html_indicators)


def is_filename_too_long(config: "ConfluenceConfig", filename: str) -> bool:
    """Check if filename exceeds maximum length.

    Args:
        config: The ConfluenceConfig instance
        filename: Name of the file to check

    Returns:
        bool: True if filename is too long, False otherwise
    """
    return len(filename) > config.file_processing.max_filename_length


def should_include_by_date(config: "ConfluenceConfig", modification_date) -> bool:
    """Check if content should be included based on last modification date.

    Args:
        config: The ConfluenceConfig instance
        modification_date: DateTime object for modification date

    Returns:
        bool: True if content should be included, False otherwise
    """
    if not config.filtering.last_modified_days:
        return True

    cutoff_date = datetime.now(UTC) - timedelta(days=config.filtering.last_modified_days)
    return modification_date >= cutoff_date


def create_minimal_config() -> "ConfluenceConfig":
    """Create minimal config for testing scenarios.

    Returns:
        ConfluenceConfig: Minimal configuration with safe defaults
    """
    from .schema import ConfluenceConfig

    config = ConfluenceConfig()
    config.basic.spaces = ["DEFAULT_SPACE"]
    config.performance.parallel_downloads = False
    config.performance.enable_metrics = False
    return config
