"""Configuration Builder for Confluence.

This module provides a single entry point for creating a validated
ConfluenceConfig object from a raw source bean. It encapsulates
transformation, backward-compatibility, and instantiation logic.
"""

import logging
from typing import Any, Dict, List

from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceConfigurationError
from pydantic import ValidationError

from .schema import ConfluenceConfig

logger = logging.getLogger(__name__)


def _parse_comma_separated_string(items_str: Any) -> List[str]:
    """
    Parses a comma-separated string into a list of cleaned strings.
    Returns an empty list if the input is not a string or is empty.
    """
    if not items_str or not isinstance(items_str, str):
        return []
    return [item.strip() for item in items_str.split(",") if item.strip()]


def _transform_flat_to_nested(flat_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transform flat configuration structure to nested structure expected by Pydantic.

    Maps flat keys to their appropriate nested sections:
    - attachment-related keys → attachments section
    - basic configuration keys → basic section
    - filtering keys → filtering section
    - etc.
    """
    structured = {}

    # Define mapping of flat keys to nested structure
    key_mappings = {
        # Attachment configuration
        "include_attachments": ("attachments", "include_attachments"),
        "file_extensions": ("attachments", "file_extensions"),
        "extract_drawio_as_documents": ("attachments", "extract_drawio_as_documents"),
        "include_drawio_png_exports": ("attachments", "include_drawio_png_exports"),

        # Basic configuration
        "max_results": ("basic", "max_results"),
        "export_format": ("basic", "export_format"),
        "include_content_in_metadata": ("basic", "include_content_in_metadata"),
        "include_child_pages": ("basic", "include_child_pages"),
        "child_page_depth": ("basic", "child_page_depth"),

        # Filtering configuration
        "labels": ("filtering", "labels"),
        "exclude_labels": ("filtering", "exclude_labels"),
        "last_modified_days": ("filtering", "last_modified_days"),
        "custom_cql": ("filtering", "custom_cql"),

        # Auth configuration
        "confluence_auth_mode": ("auth", "confluence_auth_mode"),
    }

    # Transform mapped keys
    for flat_key, value in flat_config.items():
        if flat_key in key_mappings:
            section, nested_key = key_mappings[flat_key]
            if section not in structured:
                structured[section] = {}
            structured[section][nested_key] = value
        else:
            # Keep unmapped keys at top level for backward compatibility
            structured[flat_key] = value

    return structured


def build_config_from_source(source: SourceBean) -> ConfluenceConfig:
    """
    Creates a validated instance of ConfluenceConfig from a SourceBean.
    Handles legacy keys and guarantees at least one space.
    """
    raw_config = source.parse_configuration()

    # ---------- 1.  Transform flat structure to nested structure ----------
    structured_config = _transform_flat_to_nested(raw_config)

    # ---------- 2.  Backward-compatibility for space_key ----------
    structured_config.setdefault("basic", {})
    structured_config["basic"].setdefault("spaces", [])

    if "space_key" in structured_config:
        space_key = structured_config.pop("space_key")
        if space_key and space_key not in structured_config["basic"]["spaces"]:
            structured_config["basic"]["spaces"].append(space_key)

    # ---------- 3.  Comma-separated strings → lists ----------
    for section in ["attachments", "filtering"]:
        if section in structured_config:
            for key in ["labels", "exclude_labels", "file_extensions"]:
                if key in structured_config[section] and isinstance(structured_config[section][key], str):
                    structured_config[section][key] = _parse_comma_separated_string(structured_config[section][key])

    # Handle top-level comma-separated strings for backward compatibility
    for key in ["labels", "exclude_labels", "file_extensions"]:
        if key in structured_config and isinstance(structured_config[key], str):
            structured_config[key] = _parse_comma_separated_string(structured_config[key])

    # ---------- 4.  Validate ----------
    try:
        config = ConfluenceConfig.model_validate(structured_config)
        logger.debug(f"Validated configuration created for source '{source.code}'.")
        return config
    except ValidationError as e:
        raise ConfluenceConfigurationError(f"Configuration validation failed: {e}") from e