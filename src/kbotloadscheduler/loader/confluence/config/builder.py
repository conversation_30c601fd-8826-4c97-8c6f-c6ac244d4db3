"""Configuration Builder for Confluence.

This module provides a single entry point for creating a validated
ConfluenceConfig object from a raw source bean. It encapsulates
transformation, backward-compatibility, and instantiation logic.
"""

import logging
from typing import Any, Dict, List

from kbotloadscheduler.bean.beans import SourceBean
from kbotloadscheduler.exceptions.confluence_exceptions import ConfluenceConfigurationError
from pydantic import ValidationError

from .schema import ConfluenceConfig

logger = logging.getLogger(__name__)


def _parse_comma_separated_string(items_str: Any) -> List[str]:
    """
    Parses a comma-separated string into a list of cleaned strings.
    Returns an empty list if the input is not a string or is empty.
    """
    if not items_str or not isinstance(items_str, str):
        return []
    return [item.strip() for item in items_str.split(",") if item.strip()]


def build_config_from_source(source: SourceBean) -> ConfluenceConfig:
    """
    Creates a validated instance of ConfluenceConfig from a SourceBean.
    Handles legacy keys and guarantees at least one space.
    """
    raw_config = source.parse_configuration()

    # ---------- 1.  Backward-compatibility for space_key ----------
    raw_config.setdefault("basic", {})
    raw_config["basic"].setdefault("spaces", [])

    if "space_key" in raw_config:
        space_key = raw_config.pop("space_key")
        if space_key and space_key not in raw_config["basic"]["spaces"]:
            raw_config["basic"]["spaces"].append(space_key)

    # ---------- 2.  Comma-separated strings → lists ----------
    for key in ["labels", "exclude_labels", "file_extensions"]:
        if key in raw_config and isinstance(raw_config[key], str):
            raw_config[key] = _parse_comma_separated_string(raw_config[key])

    # ---------- 3.  Validate ----------
    try:
        config = ConfluenceConfig.model_validate(raw_config)
        logger.debug(f"Validated configuration created for source '{source.code}'.")
        return config
    except ValidationError as e:
        raise ConfluenceConfigurationError(f"Configuration validation failed: {e}") from e