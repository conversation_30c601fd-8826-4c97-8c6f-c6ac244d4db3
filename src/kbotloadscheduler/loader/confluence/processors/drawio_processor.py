"""
Dedicated processor for Draw.io diagram handling.

This module encapsulates the entire Draw.io processing workflow:
- Diagram detection and retrieval
- Metadata extraction
- Markdown generation
"""
import logging
from typing import Dict, Optional

from kbotloadscheduler.bean.beans import DocumentBean
from ..config.schema import ConfluenceConfig

from .drawio_detector import DrawioDetector, DrawioDiagram
from .drawio_extractor import DrawioExtractor
from .drawio_markdown_generator import DrawioMarkdownGenerator

logger = logging.getLogger(__name__)


class DrawioProcessor:
    """
    Encapsulates the complete Draw.io diagram processing workflow.
    """

    def __init__(self, client):
        self.client = client
        self.detector = DrawioDetector()
        self.extractor = DrawioExtractor()
        self.generator = DrawioMarkdownGenerator()

    def generate_drawio_markdown(self, diagram_id: str, document: DocumentBean,
                                 page_data: Dict, config: ConfluenceConfig) -> str:
        """
        Generate markdown content for a specific Draw.io diagram.

        This method is now a pure "processor". It expects the `page_data`
        (containing the page's HTML/XML content) to be fetched by the caller
        (e.g., ContentDownloader).

        Args:
            diagram_id: The unique ID of the diagram to process.
            document: The DocumentBean representing the diagram.
            page_data: The full page data dictionary from the Confluence API.
            config: The loader configuration.

        Returns:
            A string containing the generated markdown for the diagram.
        """
        try:
            # This logic remains the same: it extracts content from the pre-fetched page_data.
            page_view_content = page_data.get('body', {}).get('view', {}).get('value', '')
            page_storage_content = page_data.get('body', {}).get('storage', {}).get('value', '')
            page_content = page_view_content or page_storage_content
            page_id = page_data.get('id', '')

            if not page_content:
                logger.warning(f"Empty page content for {page_id}, cannot process Draw.io diagram.")
                return f"# {document.name}\n\n*Draw.io diagram not available - empty page content*"

            diagrams = self.detector.detect_drawio_diagrams(page_content, page_id)
            target_diagram = self._find_diagram_by_id(diagrams, diagram_id)

            if not target_diagram:
                logger.warning(f"Draw.io diagram with ID {diagram_id} not found in page {page_id}")
                return DrawioMarkdownGenerator.generate_basic_drawio_markdown(None, document, page_data)

            # This logic also remains: if the diagram is an attachment, fetch its content.
            # This is a valid use of the client inside the processor.
            if target_diagram.attachment_id and not target_diagram.xml_content:
                logger.info(f"Diagram points to attachment ID {target_diagram.attachment_id}. Fetching content.")
                try:
                    # The client's get_attachment_content method is already resilient.
                    attachment_content = self.client.get_attachment_content(target_diagram.attachment_id)
                    target_diagram.xml_content = attachment_content.decode('utf-8')
                    logger.info("Successfully fetched and decoded attachment content.")
                except Exception as e:
                    logger.error(f"Failed to fetch attachment {target_diagram.attachment_id}: {e}")

            metadata = None
            if target_diagram.xml_content:
                metadata = self.extractor.extract_metadata(
                    xml_content=target_diagram.xml_content,
                    diagram_title=target_diagram.title or document.name
                )

            if metadata and (metadata.shapes or metadata.all_text):
                # The call to _generate_rich_markdown is where one of the key fixes happens.
                return self._generate_rich_markdown(target_diagram, metadata, document, page_data)
            else:
                logger.warning(f"No metadata could be extracted for diagram {diagram_id}. Generating basic markdown.")
                return DrawioMarkdownGenerator.generate_basic_drawio_markdown(target_diagram, document, page_data)

        except Exception as e:
            logger.error(f"Critical error in generate_drawio_markdown for {diagram_id}: {e}", exc_info=True)
            return f"# {document.name}\n\n*Error processing Draw.io content: {str(e)}*"

    def _find_diagram_by_id(self, diagrams: list[DrawioDiagram], target_id: str) -> Optional[DrawioDiagram]:
        for diagram in diagrams:
            if diagram.diagram_id == target_id:
                return diagram
        return None

    def _generate_rich_markdown(self, diagram: DrawioDiagram, metadata,
                                document: DocumentBean, page_data: Dict) -> str:
        source_info = {
            'page_title': page_data.get('title', 'Confluence Page'),
            'page_url': document.path,
            'last_modified': page_data.get('version', {}).get('when'),
            'created_by': page_data.get('version', {}).get('by', {}).get('displayName'),
            'title': diagram.title or document.name
        }

        return self.generator.generate_markdown(metadata, source_info, page_data.get('body', {}).get('view', {}).get('value', '') or page_data.get('body', {}).get('storage', {}).get('value', ''))
